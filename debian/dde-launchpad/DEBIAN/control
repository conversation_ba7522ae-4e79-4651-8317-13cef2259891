Package: dde-launchpad
Version: 2.0.4
Architecture: amd64
Maintainer: <PERSON> <<EMAIL>>
Installed-Size: 3339
Depends: libappstreamqt3 (>= 1.0.3), libc6 (>= 2.38), libdde-shell (>= 2.0.3), libdtk6core (>= 6.0), libdtk6gui (>= 6.0.19), libgcc-s1 (>= 3.0), libglib2.0-0 (>= 2.40.0), libqt6core6 (>= 6.8.0), libqt6dbus6 (>= 6.8.0), libqt6gui6 (>= 6.1.2), libqt6qml6 (>= 6.6.0), libqt6quick6 (>= 6.6.0), libqt6waylandclient6 (>= 6.6.0), libstdc++6 (>= 11), libwayland-client0 (>= 1.20.0), qt6-base-private-abi (= 6.8.0), dde-application-manager (>> 1.2.2), dde-application-wizard-daemon-compat, libdtk6declarative (>= 6.0.19), qml6-module-qtquick-layouts, qml6-module-qtquick-window, qml6-module-qtquick-controls, qml6-module-qtquick-controls2-styles-chameleon, qml6-module-qt-labs-platform, qml6-module-qt5compat-graphicaleffects
Conflicts: dde-launcher
Replaces: dde-launcher
Section: DDE
Priority: optional
Description: The "launcher" or "start menu" component for DDE
 Display all installed applications and allow user to launch applications, which is convenient for users to operate quickly
