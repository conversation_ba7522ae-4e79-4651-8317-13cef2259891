/****************************************************************************
** Generated QML type registration code
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <QtQml/qqml.h>
#include <QtQml/qqmlmoduleregistration.h>

#if __has_include(<debughelper.h>)
#  include <debughelper.h>
#endif
#if __has_include(<desktopintegration.h>)
#  include <desktopintegration.h>
#endif
#if __has_include(<inputeventitem.h>)
#  include <inputeventitem.h>
#endif
#if __has_include(<launchercontroller.h>)
#  include <launchercontroller.h>
#endif


#if !defined(QT_STATIC)
#define Q_QMLTYPE_EXPORT Q_DECL_EXPORT
#else
#define Q_QMLTYPE_EXPORT
#endif
Q_QMLTYPE_EXPORT void qml_register_types_org_deepin_launchpad()
{
    QT_WARNING_PUSH QT_WARNING_DISABLE_DEPRECATED
    qmlRegisterTypesAndRevisions<DebugHelper>("org.deepin.launchpad", 1);
    qmlRegisterTypesAndRevisions<DebugQuickItem>("org.deepin.launchpad", 1);
    qmlRegisterTypesAndRevisions<DesktopIntegration>("org.deepin.launchpad", 1);
    qmlRegisterTypesAndRevisions<InputEventItem>("org.deepin.launchpad", 1);
    qmlRegisterAnonymousType<QQuickItem, 254>("org.deepin.launchpad", 1);
    qmlRegisterTypesAndRevisions<LauncherController>("org.deepin.launchpad", 1);
    QT_WARNING_POP
    qmlRegisterModule("org.deepin.launchpad", 1, 0);
}

static const QQmlModuleRegistration orgdeepinlaunchpadRegistration("org.deepin.launchpad", qml_register_types_org_deepin_launchpad);
