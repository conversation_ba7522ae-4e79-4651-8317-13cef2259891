/****************************************************************************
** Meta object code from reading C++ file 'launchercontroller.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../launchercontroller.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'launchercontroller.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSLauncherControllerENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSLauncherControllerENDCLASS = QtMocHelpers::stringData(
    "LauncherController",
    "QML.Element",
    "QML.Singleton",
    "true",
    "currentFrameChanged",
    "",
    "visibleChanged",
    "visible",
    "Closed",
    "Shown",
    "VisibleChanged",
    "hideWithTimer",
    "setAvoidHide",
    "avoidHide",
    "cancelHide",
    "adjustFontWeight",
    "f",
    "QFont::Weight",
    "weight",
    "closeAllPopups",
    "showHelp",
    "currentFrame",
    "Visible"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSLauncherControllerENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
      11,   18, // methods
       3,  105, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   84,    5, 0x06,    4 /* Public */,
       6,    1,   85,    5, 0x06,    5 /* Public */,
       8,    0,   88,    5, 0x06,    7 /* Public */,
       9,    0,   89,    5, 0x06,    8 /* Public */,
      10,    1,   90,    5, 0x06,    9 /* Public */,

 // methods: name, argc, parameters, tag, flags, initial metatype offsets
      11,    0,   93,    5, 0x02,   11 /* Public */,
      12,    1,   94,    5, 0x02,   12 /* Public */,
      14,    0,   97,    5, 0x02,   14 /* Public */,
      15,    2,   98,    5, 0x02,   15 /* Public */,
      19,    0,  103,    5, 0x02,   18 /* Public */,
      20,    0,  104,    5, 0x02,   19 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    7,

 // methods: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   13,
    QMetaType::Void,
    QMetaType::QFont, QMetaType::QFont, 0x80000000 | 17,   16,   18,
    QMetaType::Void,
    QMetaType::Void,

 // properties: name, type, flags, notifyId, revision
       7, QMetaType::Bool, 0x00015103, uint(1), 0,
      21, QMetaType::QString, 0x00015103, uint(0), 0,
      22, QMetaType::Bool, 0x00015001, uint(4), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject LauncherController::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSLauncherControllerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSLauncherControllerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'visible'
        bool,
        // property 'currentFrame'
        QString,
        // property 'Visible'
        bool,
        // Q_OBJECT / Q_GADGET
        LauncherController,
        // method 'currentFrameChanged'
        void,
        // method 'visibleChanged'
        void,
        bool,
        // method 'Closed'
        void,
        // method 'Shown'
        void,
        // method 'VisibleChanged'
        void,
        bool,
        // method 'hideWithTimer'
        void,
        // method 'setAvoidHide'
        void,
        bool,
        // method 'cancelHide'
        void,
        // method 'adjustFontWeight'
        QFont,
        const QFont &,
        QFont::Weight,
        // method 'closeAllPopups'
        void,
        // method 'showHelp'
        void
    >,
    nullptr
} };

void LauncherController::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<LauncherController *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->currentFrameChanged(); break;
        case 1: _t->visibleChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 2: _t->Closed(); break;
        case 3: _t->Shown(); break;
        case 4: _t->VisibleChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 5: _t->hideWithTimer(); break;
        case 6: _t->setAvoidHide((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 7: _t->cancelHide(); break;
        case 8: { QFont _r = _t->adjustFontWeight((*reinterpret_cast< std::add_pointer_t<QFont>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QFont::Weight>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QFont*>(_a[0]) = std::move(_r); }  break;
        case 9: _t->closeAllPopups(); break;
        case 10: _t->showHelp(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (LauncherController::*)();
            if (_t _q_method = &LauncherController::currentFrameChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (LauncherController::*)(bool );
            if (_t _q_method = &LauncherController::visibleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (LauncherController::*)();
            if (_t _q_method = &LauncherController::Closed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (LauncherController::*)();
            if (_t _q_method = &LauncherController::Shown; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (LauncherController::*)(bool );
            if (_t _q_method = &LauncherController::VisibleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<LauncherController *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->visible(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->currentFrame(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->visible(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<LauncherController *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setVisible(*reinterpret_cast< bool*>(_v)); break;
        case 1: _t->setCurrentFrame(*reinterpret_cast< QString*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *LauncherController::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *LauncherController::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSLauncherControllerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int LauncherController::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void LauncherController::currentFrameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void LauncherController::visibleChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void LauncherController::Closed()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void LauncherController::Shown()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void LauncherController::VisibleChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
