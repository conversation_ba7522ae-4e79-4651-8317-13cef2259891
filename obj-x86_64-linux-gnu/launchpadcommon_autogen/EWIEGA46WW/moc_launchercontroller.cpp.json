{"classes": [{"classInfos": [{"name": "QML.Element", "value": "LauncherController"}, {"name": "QML.Singleton", "value": "true"}], "className": "LauncherController", "lineNumber": 14, "methods": [{"access": "public", "index": 5, "name": "hideWithTimer", "returnType": "void"}, {"access": "public", "arguments": [{"name": "avoidHide", "type": "bool"}], "index": 6, "name": "setAvoidHide", "returnType": "void"}, {"access": "public", "index": 7, "name": "cancelHide", "returnType": "void"}, {"access": "public", "arguments": [{"name": "f", "type": "QFont"}, {"name": "weight", "type": "QFont::Weight"}], "index": 8, "name": "adjustFontWeight", "returnType": "QFont"}, {"access": "public", "index": 9, "name": "closeAllPopups", "returnType": "void"}, {"access": "public", "index": 10, "name": "showHelp", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "visible", "notify": "visibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "currentFrame", "notify": "currentFrameChanged", "read": "currentFrame", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCurrentFrame"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "Visible", "notify": "VisibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "LauncherController", "signals": [{"access": "public", "index": 0, "name": "currentFrameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 1, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "Closed", "returnType": "void"}, {"access": "public", "index": 3, "name": "Shown", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 4, "name": "VisibleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "launchercontroller.h", "outputRevision": 68}