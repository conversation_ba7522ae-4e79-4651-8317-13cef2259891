{"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "InputEventItem", "lineNumber": 11, "object": true, "qualifiedClassName": "InputEventItem", "signals": [{"access": "public", "arguments": [{"name": "input", "type": "QString"}], "index": 0, "name": "inputReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "inputeventitem.h", "outputRevision": 68}