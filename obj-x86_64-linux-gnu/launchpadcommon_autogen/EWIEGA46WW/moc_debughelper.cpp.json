{"classes": [{"classInfos": [{"name": "QML.Element", "value": "DebugItem"}, {"name": "QML.Attached", "value": "DebugQuickItem"}], "className": "DebugQuickItem", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "DebugQuickItem", "signals": [{"access": "public", "index": 0, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "DebugHelper"}, {"name": "QML.Singleton", "value": "true"}], "className": "DebugHelper", "lineNumber": 29, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "qtDebugEnabled", "read": "qtDebugEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_useRegularWindow", "name": "useRegularWindow", "notify": "onUseRegularWindowChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_avoidLaunchApp", "name": "avoidLaunchApp", "notify": "onAvoidLaunchAppChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_avoidHideWindow", "name": "avoidHideWindow", "notify": "onAvoidHideWindowChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "member": "m_itemBoundingEnabled", "name": "itemBoundingEnabled", "notify": "onItemBoundingEnabledChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "DebugHelper", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "onUseRegularWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 1, "name": "onAvoidLaunchAppChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 2, "name": "onAvoidHideWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "onItemBoundingEnabledChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "debughelper.h", "outputRevision": 68}