/****************************************************************************
** Meta object code from reading C++ file 'desktopintegration.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../desktopintegration.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'desktopintegration.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSDesktopIntegrationENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSDesktopIntegrationENDCLASS = QtMocHelpers::stringData(
    "DesktopIntegration",
    "QML.Element",
    "QML.Singleton",
    "true",
    "dockPositionChanged",
    "",
    "dockGeometryChanged",
    "dockSpacingChanged",
    "backgroundUrlChanged",
    "opacityChanged",
    "scaleFactorChanged",
    "currentDE",
    "isTreeLand",
    "openSystemSettings",
    "launchByDesktopId",
    "desktopId",
    "environmentVariable",
    "env",
    "disableScale",
    "setDisableScale",
    "showFolder",
    "QStandardPaths::StandardLocation",
    "location",
    "showUrl",
    "url",
    "appIsCompulsoryForDesktop",
    "appIsDummyPackage",
    "isDockedApp",
    "sendToDock",
    "removeFromDock",
    "isOnDesktop",
    "sendToDesktop",
    "removeFromDesktop",
    "isAutoStart",
    "setAutoStart",
    "on",
    "shouldSkipConfirmUninstallDialog",
    "uninstallApp",
    "scaleFactor",
    "dockPosition",
    "Qt::ArrowType",
    "dockGeometry",
    "dockSpacing",
    "backgroundUrl",
    "opacity"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSDesktopIntegrationENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
      29,   18, // methods
       6,  263, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,  192,    5, 0x06,    7 /* Public */,
       6,    0,  193,    5, 0x06,    8 /* Public */,
       7,    0,  194,    5, 0x06,    9 /* Public */,
       8,    0,  195,    5, 0x06,   10 /* Public */,
       9,    0,  196,    5, 0x06,   11 /* Public */,
      10,    0,  197,    5, 0x06,   12 /* Public */,

 // methods: name, argc, parameters, tag, flags, initial metatype offsets
      11,    0,  198,    5, 0x02,   13 /* Public */,
      12,    0,  199,    5, 0x02,   14 /* Public */,
      13,    0,  200,    5, 0x02,   15 /* Public */,
      14,    1,  201,    5, 0x02,   16 /* Public */,
      16,    1,  204,    5, 0x02,   18 /* Public */,
      18,    1,  207,    5, 0x02,   20 /* Public */,
      19,    2,  210,    5, 0x02,   22 /* Public */,
      20,    1,  215,    5, 0x02,   25 /* Public */,
      23,    1,  218,    5, 0x02,   27 /* Public */,
      25,    1,  221,    5, 0x02,   29 /* Public */,
      26,    1,  224,    5, 0x02,   31 /* Public */,
      27,    1,  227,    5, 0x102,   33 /* Public | MethodIsConst  */,
      28,    1,  230,    5, 0x02,   35 /* Public */,
      29,    1,  233,    5, 0x02,   37 /* Public */,
      30,    1,  236,    5, 0x102,   39 /* Public | MethodIsConst  */,
      31,    1,  239,    5, 0x02,   41 /* Public */,
      32,    1,  242,    5, 0x02,   43 /* Public */,
      33,    1,  245,    5, 0x102,   45 /* Public | MethodIsConst  */,
      34,    2,  248,    5, 0x02,   47 /* Public */,
      34,    1,  253,    5, 0x22,   50 /* Public | MethodCloned */,
      36,    1,  256,    5, 0x102,   52 /* Public | MethodIsConst  */,
      37,    1,  259,    5, 0x02,   54 /* Public */,
      38,    0,  262,    5, 0x102,   56 /* Public | MethodIsConst  */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // methods: parameters
    QMetaType::QString,
    QMetaType::Bool,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::QString, QMetaType::QString,   17,
    QMetaType::Double, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::Double,   15,   18,
    QMetaType::Void, 0x80000000 | 21,   22,
    QMetaType::Void, QMetaType::QString,   24,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,   15,   35,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Bool, QMetaType::QString,   15,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Double,

 // properties: name, type, flags, notifyId, revision
      39, 0x80000000 | 40, 0x00015009, uint(0), 0,
      41, QMetaType::QRect, 0x00015001, uint(1), 0,
      42, QMetaType::UInt, 0x00015001, uint(2), 0,
      43, QMetaType::QString, 0x00015001, uint(3), 0,
      44, QMetaType::QReal, 0x00015801, uint(4), 0,
      38, QMetaType::Double, 0x00015801, uint(5), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject DesktopIntegration::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSDesktopIntegrationENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSDesktopIntegrationENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'dockPosition'
        Qt::ArrowType,
        // property 'dockGeometry'
        QRect,
        // property 'dockSpacing'
        uint,
        // property 'backgroundUrl'
        QString,
        // property 'opacity'
        qreal,
        // property 'scaleFactor'
        double,
        // Q_OBJECT / Q_GADGET
        DesktopIntegration,
        // method 'dockPositionChanged'
        void,
        // method 'dockGeometryChanged'
        void,
        // method 'dockSpacingChanged'
        void,
        // method 'backgroundUrlChanged'
        void,
        // method 'opacityChanged'
        void,
        // method 'scaleFactorChanged'
        void,
        // method 'currentDE'
        QString,
        // method 'isTreeLand'
        bool,
        // method 'openSystemSettings'
        void,
        // method 'launchByDesktopId'
        void,
        const QString &,
        // method 'environmentVariable'
        QString,
        const QString &,
        // method 'disableScale'
        double,
        const QString &,
        // method 'setDisableScale'
        void,
        const QString &,
        double,
        // method 'showFolder'
        void,
        QStandardPaths::StandardLocation,
        // method 'showUrl'
        void,
        const QString &,
        // method 'appIsCompulsoryForDesktop'
        bool,
        const QString &,
        // method 'appIsDummyPackage'
        bool,
        const QString &,
        // method 'isDockedApp'
        bool,
        const QString &,
        // method 'sendToDock'
        void,
        const QString &,
        // method 'removeFromDock'
        void,
        const QString &,
        // method 'isOnDesktop'
        bool,
        const QString &,
        // method 'sendToDesktop'
        void,
        const QString &,
        // method 'removeFromDesktop'
        void,
        const QString &,
        // method 'isAutoStart'
        bool,
        const QString &,
        // method 'setAutoStart'
        void,
        const QString &,
        bool,
        // method 'setAutoStart'
        void,
        const QString &,
        // method 'shouldSkipConfirmUninstallDialog'
        bool,
        const QString &,
        // method 'uninstallApp'
        void,
        const QString &,
        // method 'scaleFactor'
        double
    >,
    nullptr
} };

void DesktopIntegration::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DesktopIntegration *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->dockPositionChanged(); break;
        case 1: _t->dockGeometryChanged(); break;
        case 2: _t->dockSpacingChanged(); break;
        case 3: _t->backgroundUrlChanged(); break;
        case 4: _t->opacityChanged(); break;
        case 5: _t->scaleFactorChanged(); break;
        case 6: { QString _r = _t->currentDE();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 7: { bool _r = _t->isTreeLand();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 8: _t->openSystemSettings(); break;
        case 9: _t->launchByDesktopId((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 10: { QString _r = _t->environmentVariable((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 11: { double _r = _t->disableScale((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< double*>(_a[0]) = std::move(_r); }  break;
        case 12: _t->setDisableScale((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 13: _t->showFolder((*reinterpret_cast< std::add_pointer_t<QStandardPaths::StandardLocation>>(_a[1]))); break;
        case 14: _t->showUrl((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 15: { bool _r = _t->appIsCompulsoryForDesktop((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 16: { bool _r = _t->appIsDummyPackage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 17: { bool _r = _t->isDockedApp((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: _t->sendToDock((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 19: _t->removeFromDock((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 20: { bool _r = _t->isOnDesktop((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 21: _t->sendToDesktop((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 22: _t->removeFromDesktop((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 23: { bool _r = _t->isAutoStart((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 24: _t->setAutoStart((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 25: _t->setAutoStart((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 26: { bool _r = _t->shouldSkipConfirmUninstallDialog((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 27: _t->uninstallApp((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 28: { double _r = _t->scaleFactor();
            if (_a[0]) *reinterpret_cast< double*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::dockPositionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::dockGeometryChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::dockSpacingChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::backgroundUrlChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::opacityChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (DesktopIntegration::*)();
            if (_t _q_method = &DesktopIntegration::scaleFactorChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<DesktopIntegration *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< Qt::ArrowType*>(_v) = _t->dockPosition(); break;
        case 1: *reinterpret_cast< QRect*>(_v) = _t->dockGeometry(); break;
        case 2: *reinterpret_cast< uint*>(_v) = _t->dockSpacing(); break;
        case 3: *reinterpret_cast< QString*>(_v) = _t->backgroundUrl(); break;
        case 4: *reinterpret_cast< qreal*>(_v) = _t->opacity(); break;
        case 5: *reinterpret_cast< double*>(_v) = _t->scaleFactor(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *DesktopIntegration::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DesktopIntegration::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSDesktopIntegrationENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DesktopIntegration::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 29)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 29;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 29)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 29;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void DesktopIntegration::dockPositionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void DesktopIntegration::dockGeometryChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void DesktopIntegration::dockSpacingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void DesktopIntegration::backgroundUrlChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void DesktopIntegration::opacityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void DesktopIntegration::scaleFactorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}
QT_WARNING_POP
