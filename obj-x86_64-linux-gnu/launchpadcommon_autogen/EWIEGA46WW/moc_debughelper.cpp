/****************************************************************************
** Meta object code from reading C++ file 'debughelper.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../debughelper.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'debughelper.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSDebugQuickItemENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSDebugQuickItemENDCLASS = QtMocHelpers::stringData(
    "DebugQuickItem",
    "QML.Element",
    "DebugItem",
    "QML.Attached",
    "colorChanged",
    "",
    "color"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSDebugQuickItemENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
       1,   18, // methods
       1,   25, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // classinfo: key, value
       1,    2,
       3,    0,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   24,    5, 0x06,    2 /* Public */,

 // signals: parameters
    QMetaType::Void,

 // properties: name, type, flags, notifyId, revision
       6, QMetaType::QColor, 0x00015903, uint(0), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject DebugQuickItem::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSDebugQuickItemENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSDebugQuickItemENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'color'
        QColor,
        // Q_OBJECT / Q_GADGET
        DebugQuickItem,
        // method 'colorChanged'
        void
    >,
    nullptr
} };

void DebugQuickItem::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DebugQuickItem *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->colorChanged(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DebugQuickItem::*)();
            if (_t _q_method = &DebugQuickItem::colorChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<DebugQuickItem *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QColor*>(_v) = _t->color(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<DebugQuickItem *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setColor(*reinterpret_cast< QColor*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
    (void)_a;
}

const QMetaObject *DebugQuickItem::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DebugQuickItem::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSDebugQuickItemENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DebugQuickItem::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 1)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 1;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    }
    return _id;
}

// SIGNAL 0
void DebugQuickItem::colorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSDebugHelperENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSDebugHelperENDCLASS = QtMocHelpers::stringData(
    "DebugHelper",
    "QML.Element",
    "QML.Singleton",
    "true",
    "onUseRegularWindowChanged",
    "",
    "onAvoidLaunchAppChanged",
    "onAvoidHideWindowChanged",
    "onItemBoundingEnabledChanged",
    "qtDebugEnabled",
    "useRegularWindow",
    "avoidLaunchApp",
    "avoidHideWindow",
    "itemBoundingEnabled"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSDebugHelperENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
       4,   18, // methods
       5,   54, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    1,   42,    5, 0x06,    6 /* Public */,
       6,    1,   45,    5, 0x06,    8 /* Public */,
       7,    1,   48,    5, 0x06,   10 /* Public */,
       8,    1,   51,    5, 0x06,   12 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    5,
    QMetaType::Void, QMetaType::Bool,    5,

 // properties: name, type, flags, notifyId, revision
       9, QMetaType::Bool, 0x00015401, uint(-1), 0,
      10, QMetaType::Bool, 0x00015003, uint(0), 0,
      11, QMetaType::Bool, 0x00015003, uint(1), 0,
      12, QMetaType::Bool, 0x00015003, uint(2), 0,
      13, QMetaType::Bool, 0x00015003, uint(3), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject DebugHelper::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSDebugHelperENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSDebugHelperENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'qtDebugEnabled'
        bool,
        // property 'useRegularWindow'
        bool,
        // property 'avoidLaunchApp'
        bool,
        // property 'avoidHideWindow'
        bool,
        // property 'itemBoundingEnabled'
        bool,
        // Q_OBJECT / Q_GADGET
        DebugHelper,
        // method 'onUseRegularWindowChanged'
        void,
        bool,
        // method 'onAvoidLaunchAppChanged'
        void,
        bool,
        // method 'onAvoidHideWindowChanged'
        void,
        bool,
        // method 'onItemBoundingEnabledChanged'
        void,
        bool
    >,
    nullptr
} };

void DebugHelper::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DebugHelper *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onUseRegularWindowChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 1: _t->onAvoidLaunchAppChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 2: _t->onAvoidHideWindowChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 3: _t->onItemBoundingEnabledChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DebugHelper::*)(bool );
            if (_t _q_method = &DebugHelper::onUseRegularWindowChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DebugHelper::*)(bool );
            if (_t _q_method = &DebugHelper::onAvoidLaunchAppChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DebugHelper::*)(bool );
            if (_t _q_method = &DebugHelper::onAvoidHideWindowChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DebugHelper::*)(bool );
            if (_t _q_method = &DebugHelper::onItemBoundingEnabledChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<DebugHelper *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->qtDebugEnabled(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->m_useRegularWindow; break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->m_avoidLaunchApp; break;
        case 3: *reinterpret_cast< bool*>(_v) = _t->m_avoidHideWindow; break;
        case 4: *reinterpret_cast< bool*>(_v) = _t->m_itemBoundingEnabled; break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<DebugHelper *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 1:
            if (_t->m_useRegularWindow != *reinterpret_cast< bool*>(_v)) {
                _t->m_useRegularWindow = *reinterpret_cast< bool*>(_v);
                Q_EMIT _t->onUseRegularWindowChanged(_t->m_useRegularWindow);
            }
            break;
        case 2:
            if (_t->m_avoidLaunchApp != *reinterpret_cast< bool*>(_v)) {
                _t->m_avoidLaunchApp = *reinterpret_cast< bool*>(_v);
                Q_EMIT _t->onAvoidLaunchAppChanged(_t->m_avoidLaunchApp);
            }
            break;
        case 3:
            if (_t->m_avoidHideWindow != *reinterpret_cast< bool*>(_v)) {
                _t->m_avoidHideWindow = *reinterpret_cast< bool*>(_v);
                Q_EMIT _t->onAvoidHideWindowChanged(_t->m_avoidHideWindow);
            }
            break;
        case 4:
            if (_t->m_itemBoundingEnabled != *reinterpret_cast< bool*>(_v)) {
                _t->m_itemBoundingEnabled = *reinterpret_cast< bool*>(_v);
                Q_EMIT _t->onItemBoundingEnabledChanged(_t->m_itemBoundingEnabled);
            }
            break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *DebugHelper::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DebugHelper::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSDebugHelperENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DebugHelper::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void DebugHelper::onUseRegularWindowChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void DebugHelper::onAvoidLaunchAppChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void DebugHelper::onAvoidHideWindowChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void DebugHelper::onItemBoundingEnabledChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
