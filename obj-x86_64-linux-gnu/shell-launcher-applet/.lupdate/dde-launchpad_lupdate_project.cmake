set(lupdate_project_file "/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/CMakeLists.txt")
set(lupdate_translations "/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_az.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_bo.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_ca.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_es.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_fi.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_fr.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_hu.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_it.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_ja.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_ko.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_nb_NO.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_pl.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_pt_BR.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_ru.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_uk.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_zh_CN.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_zh_HK.ts;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/translations/org.deepin.ds.dock.launcherapplet_zh_TW.ts")
set(lupdate_include_paths "")
set(lupdate_sources "/home/<USER>/Desktop/myrepo/dde-launchpad/qml/AppItemMenu.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/DummyAppItemMenu.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/Main.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/FullscreenFrame.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/WindowedFrame.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SideBar.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/BottomBar.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AlphabetCategoryPopup.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppList.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AppListView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/IconItemDelegate.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FreeSortListView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/GridViewContainer.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/AnalysisView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/RecentlyInstalledView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/FrequentlyUsedView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/SearchResultView.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/qml/windowed/ItemBackground.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/package/launcheritem.qml;/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/launcheritem.cpp")
set(lupdate_subproject_count 1)

set(lupdate_subproject1_source_dir "/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet")
set(lupdate_subproject1_include_paths "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/include;/usr/include/dde-shell;/usr/include/dtk6/DCore;/usr/include/x86_64-linux-gnu/qt6/QtCore;/usr/include/x86_64-linux-gnu/qt6;/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtDBus;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtXml;/usr/include/x86_64-linux-gnu/qt6;/usr/include/dtk6/DLog;/usr/include/dtk6/DGui;/usr/include/x86_64-linux-gnu/qt6/QtGui;/usr/include/x86_64-linux-gnu/qt6;/usr/include;/usr/include;/usr/include;/usr/include;/usr/include/x86_64-linux-gnu/qt6/QtNetwork;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQuick;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQml;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQmlMeta;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQmlModels;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtQmlWorkerScript;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtOpenGL;/usr/include/x86_64-linux-gnu/qt6;/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0;/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui;/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0;/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore;/usr/include;/usr/include/x86_64-linux-gnu/qt6/QtQuickControls2;/usr/include/x86_64-linux-gnu/qt6;/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils;/usr/include;/usr/include/glib-2.0;/usr/lib/x86_64-linux-gnu/glib-2.0/include;/usr/include/sysprof-6;/usr/include/libmount;/usr/include/blkid;/usr/include/gio-unix-2.0;/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils;/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick;/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick;/home/<USER>/Desktop/myrepo/dde-launchpad/src/models;/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration;/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration;/home/<USER>/Desktop/myrepo/dde-launchpad/src;/usr/include/x86_64-linux-gnu/qt6/QtConcurrent;/usr/include/x86_64-linux-gnu/qt6;/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration")
set(lupdate_subproject1_sources "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/mocs_compilation.cpp;launcheritem.cpp;launcheritem.h;/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/timestamp;/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/timestamp.rule")
set(lupdate_subproject1_excluded "")
set(lupdate_subproject1_autogen_dir "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen")
