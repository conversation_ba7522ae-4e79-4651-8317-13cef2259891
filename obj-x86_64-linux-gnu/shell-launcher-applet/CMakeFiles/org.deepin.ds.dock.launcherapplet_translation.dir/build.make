# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for org.deepin.ds.dock.launcherapplet_translation.

# Include any custom commands dependencies for this target.
include shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/compiler_depend.make

# Include the progress variables for this target.
include shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/progress.make

shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/codegen:
.PHONY : shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/codegen

org.deepin.ds.dock.launcherapplet_translation: shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/build.make
.PHONY : org.deepin.ds.dock.launcherapplet_translation

# Rule to build all files generated by this target.
shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/build: org.deepin.ds.dock.launcherapplet_translation
.PHONY : shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/build

shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet && $(CMAKE_COMMAND) -P CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/cmake_clean.cmake
.PHONY : shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/clean

shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : shell-launcher-applet/CMakeFiles/org.deepin.ds.dock.launcherapplet_translation.dir/depend

