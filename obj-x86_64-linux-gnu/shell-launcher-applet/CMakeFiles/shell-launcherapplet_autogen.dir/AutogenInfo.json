{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigExtras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtInstallPaths.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeatureCommon.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicAppleHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicGitHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicPluginHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTargetHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTestHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicToolHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Dependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellPackageMacros.cmake", "/usr/share/cmake-3.31/Modules/CMakeParseArguments.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/deps", "DEP_FILE_RULE_NAME": "shell-launcherapplet_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/launcheritem.h", "Mu", "EWIEGA46WW/moc_launcheritem.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DDE_SHELL_PACKAGE_PATH_SUBPATH=\"dde-shell\"", "DSG_DATA_DIR=\"/usr/share/dsg\"", "DSYSINFO_PREFIX=\"\"", "PREFIX=\"/usr\"", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DBUS_LIB", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICKCONTROLS2_LIB", "QT_QUICK_LIB", "QT_XML_LIB", "shell_launcherapplet_EXPORTS"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet", "/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/models", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration", "/home/<USER>/Desktop/myrepo/dde-launchpad/src", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration", "/usr/include/dde-shell", "/usr/include/dtk6/DCore", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/include/x86_64-linux-gnu/qt6", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtDBus", "/usr/include/x86_64-linux-gnu/qt6/QtXml", "/usr/include/dtk6/DLog", "/usr/include/dtk6/DGui", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtNetwork", "/usr/include/x86_64-linux-gnu/qt6/QtQuick", "/usr/include/x86_64-linux-gnu/qt6/QtQml", "/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration", "/usr/include/x86_64-linux-gnu/qt6/QtQmlMeta", "/usr/include/x86_64-linux-gnu/qt6/QtQmlModels", "/usr/include/x86_64-linux-gnu/qt6/QtQmlWorkerScript", "/usr/include/x86_64-linux-gnu/qt6/QtOpenGL", "/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore", "/usr/include/x86_64-linux-gnu/qt6/QtQuickControls2", "/usr/include/glib-2.0", "/usr/lib/x86_64-linux-gnu/glib-2.0/include", "/usr/include/sysprof-6", "/usr/include/libmount", "/usr/include/blkid", "/usr/include/gio-unix-2.0", "/usr/include/x86_64-linux-gnu/qt6/QtConcurrent", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS", "D_APPLET_CLASS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/shell-launcherapplet_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/CMakeFiles/shell-launcherapplet_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/CMakeFiles/shell-launcherapplet_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet/launcheritem.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}