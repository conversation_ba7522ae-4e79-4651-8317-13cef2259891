../plugins/org.deepin.ds.dock.launcherapplet.so: \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/crti.o \
  /usr/lib/gcc/x86_64-linux-gnu/12/crtbeginS.o \
  CMakeFiles/shell-launcherapplet.dir/shell-launcherapplet_autogen/mocs_compilation.cpp.o \
  CMakeFiles/shell-launcherapplet.dir/launcheritem.cpp.o \
  ../qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o \
  ../qml/windowed/CMakeFiles/launcher-qml-windowed_resources_2.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0_init.cpp.o \
  ../qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o \
  ../qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o \
  ../src/models/CMakeFiles/launcher-models_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models_init.cpp.o \
  ../src/models/CMakeFiles/launcher-models_init.dir/launcher-models_init_autogen/mocs_compilation.cpp.o \
  ../src/models/CMakeFiles/launcher-models_init.dir/launcher_models_init.cpp.o \
  /usr/lib/x86_64-linux-gnu/libdde-shell.so.2.0.3 \
  ../launchpadcommon.so \
  /usr/lib/x86_64-linux-gnu/libQt6QuickControls2.so.6.8.0 \
  ../qml/windowed/liblauncher-qml-windowed.a \
  /usr/lib/x86_64-linux-gnu/libQt6Quick.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6QmlMeta.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6QmlWorkerScript.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6QmlModels.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.8.0 \
  ../src/models/liblauncher-models.a \
  /usr/lib/x86_64-linux-gnu/libQt6Qml.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libgio-2.0.so \
  /usr/lib/x86_64-linux-gnu/libgobject-2.0.so \
  /usr/lib/x86_64-linux-gnu/libglib-2.0.so \
  /usr/lib/x86_64-linux-gnu/libdtk6gui.so.6.0.36 \
  /usr/lib/x86_64-linux-gnu/libdtk6core.so.6.0.36 \
  /usr/lib/x86_64-linux-gnu/libQt6Xml.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libdtk6log.so.0.0.2 \
  /usr/lib/x86_64-linux-gnu/libQt6Network.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libAppStreamQt.so.1.0.3 \
  /usr/lib/x86_64-linux-gnu/libxkbcommon.so \
  /usr/lib/x86_64-linux-gnu/libQt6WaylandClient.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6DBus.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libQt6Core.so.6.8.0 \
  /usr/lib/x86_64-linux-gnu/libGLX.so \
  /usr/lib/x86_64-linux-gnu/libOpenGL.so \
  /usr/lib/x86_64-linux-gnu/libwayland-client.so \
  /usr/lib/x86_64-linux-gnu/libwayland-cursor.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/libstdc++.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so \
  /lib/x86_64-linux-gnu/libm.so.6 \
  /lib/x86_64-linux-gnu/libmvec.so.1 \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libgcc_s.so.1 \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc.a \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so \
  /lib/x86_64-linux-gnu/libc.so.6 \
  /usr/lib/x86_64-linux-gnu/libc_nonshared.a \
  /lib64/ld-linux-x86-64.so.2 \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libgcc_s.so.1 \
  /usr/lib/gcc/x86_64-linux-gnu/12/libgcc.a \
  /usr/lib/gcc/x86_64-linux-gnu/12/crtendS.o \
  /usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/crtn.o

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/crti.o:

/usr/lib/gcc/x86_64-linux-gnu/12/crtbeginS.o:

CMakeFiles/shell-launcherapplet.dir/shell-launcherapplet_autogen/mocs_compilation.cpp.o:

CMakeFiles/shell-launcherapplet.dir/launcheritem.cpp.o:

../qml/windowed/CMakeFiles/launcher-qml-windowed_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_windowed_init.cpp.o:

../qml/windowed/CMakeFiles/launcher-qml-windowed_resources_2.dir/.qt/rcc/qrc_launcher-qml-windowed_raw_qml_0_init.cpp.o:

../qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher-qml-windowed_init_autogen/mocs_compilation.cpp.o:

../qml/windowed/CMakeFiles/launcher-qml-windowed_init.dir/launcher_qml_windowed_init.cpp.o:

../src/models/CMakeFiles/launcher-models_resources_1.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models_init.cpp.o:

../src/models/CMakeFiles/launcher-models_init.dir/launcher-models_init_autogen/mocs_compilation.cpp.o:

../src/models/CMakeFiles/launcher-models_init.dir/launcher_models_init.cpp.o:

/usr/lib/x86_64-linux-gnu/libdde-shell.so.2.0.3:

../launchpadcommon.so:

/usr/lib/x86_64-linux-gnu/libQt6QuickControls2.so.6.8.0:

../qml/windowed/liblauncher-qml-windowed.a:

/usr/lib/x86_64-linux-gnu/libQt6Quick.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6QmlMeta.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6QmlWorkerScript.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6QmlModels.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6OpenGL.so.6.8.0:

../src/models/liblauncher-models.a:

/usr/lib/x86_64-linux-gnu/libQt6Qml.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libgio-2.0.so:

/usr/lib/x86_64-linux-gnu/libgobject-2.0.so:

/usr/lib/x86_64-linux-gnu/libglib-2.0.so:

/usr/lib/x86_64-linux-gnu/libdtk6gui.so.6.0.36:

/usr/lib/x86_64-linux-gnu/libdtk6core.so.6.0.36:

/usr/lib/x86_64-linux-gnu/libQt6Xml.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libdtk6log.so.0.0.2:

/usr/lib/x86_64-linux-gnu/libQt6Network.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6Svg.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6Concurrent.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libAppStreamQt.so.1.0.3:

/usr/lib/x86_64-linux-gnu/libxkbcommon.so:

/usr/lib/x86_64-linux-gnu/libQt6WaylandClient.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6Gui.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6DBus.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libQt6Core.so.6.8.0:

/usr/lib/x86_64-linux-gnu/libGLX.so:

/usr/lib/x86_64-linux-gnu/libOpenGL.so:

/usr/lib/x86_64-linux-gnu/libwayland-client.so:

/usr/lib/x86_64-linux-gnu/libwayland-cursor.so:

/usr/lib/gcc/x86_64-linux-gnu/12/libstdc++.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libm.so:

/lib/x86_64-linux-gnu/libm.so.6:

/lib/x86_64-linux-gnu/libmvec.so.1:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libgcc_s.so.1:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc.a:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libc.so:

/lib/x86_64-linux-gnu/libc.so.6:

/usr/lib/x86_64-linux-gnu/libc_nonshared.a:

/lib64/ld-linux-x86-64.so.2:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc_s.so:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/libgcc_s.so.1:

/usr/lib/gcc/x86_64-linux-gnu/12/libgcc.a:

/usr/lib/gcc/x86_64-linux-gnu/12/crtendS.o:

/usr/lib/gcc/x86_64-linux-gnu/12/../../../x86_64-linux-gnu/crtn.o:
