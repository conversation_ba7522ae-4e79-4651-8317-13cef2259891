# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for dde-launchpad_lupdate.

# Include any custom commands dependencies for this target.
include shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/compiler_depend.make

# Include the progress variables for this target.
include shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/progress.make

shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate: /usr/lib/qt6/bin/lupdate
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet && /usr/bin/cmake -DIN_FILE=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/.lupdate/dde-launchpad_lupdate_project.cmake -DOUT_FILE=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/.lupdate/dde-launchpad_lupdate_project.json -P /usr/lib/x86_64-linux-gnu/cmake/Qt6LinguistTools/GenerateLUpdateProject.cmake
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet && /usr/lib/qt6/bin/lupdate -project /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/.lupdate/dde-launchpad_lupdate_project.json -no-obsolete -no-ui-lines -locations none

shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/codegen:
.PHONY : shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/codegen

dde-launchpad_lupdate: shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate
dde-launchpad_lupdate: shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/build.make
.PHONY : dde-launchpad_lupdate

# Rule to build all files generated by this target.
shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/build: dde-launchpad_lupdate
.PHONY : shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/build

shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet && $(CMAKE_COMMAND) -P CMakeFiles/dde-launchpad_lupdate.dir/cmake_clean.cmake
.PHONY : shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/clean

shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/shell-launcher-applet /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : shell-launcher-applet/CMakeFiles/dde-launchpad_lupdate.dir/depend

