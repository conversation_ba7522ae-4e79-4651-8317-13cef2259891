/****************************************************************************
** Meta object code from reading C++ file 'launcher1adaptor.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "launcher1adaptor.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'launcher1adaptor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSLauncher1AdaptorENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSLauncher1AdaptorENDCLASS = QtMocHelpers::stringData(
    "Launcher1Adaptor",
    "D-Bus Interface",
    "org.deepin.dde.Launcher1",
    "D-Bus Introspection",
    "  <interface name=\"org.deepin.dde.Launcher1\">\n    <property access="
    "\"read\" type=\"b\" name=\"Visible\"/>\n    <method name=\"Exit\"/>\n "
    "   <method name=\"Show\"/>\n    <method name=\"Hide\"/>\n    <method n"
    "ame=\"Toggle\"/>\n    <method name=\"ShowByMode\">\n      <arg directi"
    "on=\"in\" type=\"x\"/>\n    </method>\n    <signal name=\"Closed\"/>\n"
    "    <signal name=\"Shown\"/>\n    <signal name=\"VisibleChanged\">\n  "
    "    <arg direction=\"out\" type=\"b\" name=\"visible\"/>\n    </signal"
    ">\n  </interface>\n",
    "Closed",
    "",
    "Shown",
    "VisibleChanged",
    "visible",
    "Exit",
    "Hide",
    "Show",
    "ShowByMode",
    "in0",
    "Toggle",
    "Visible"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSLauncher1AdaptorENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
       8,   18, // methods
       1,   78, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // classinfo: key, value
       1,    2,
       3,    4,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       5,    0,   66,    6, 0x06,    2 /* Public */,
       7,    0,   67,    6, 0x06,    3 /* Public */,
       8,    1,   68,    6, 0x06,    4 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      10,    0,   71,    6, 0x0a,    6 /* Public */,
      11,    0,   72,    6, 0x0a,    7 /* Public */,
      12,    0,   73,    6, 0x0a,    8 /* Public */,
      13,    1,   74,    6, 0x0a,    9 /* Public */,
      15,    0,   77,    6, 0x0a,   11 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,    9,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::LongLong,   14,
    QMetaType::Void,

 // properties: name, type, flags, notifyId, revision
      16, QMetaType::Bool, 0x00015001, uint(-1), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject Launcher1Adaptor::staticMetaObject = { {
    QMetaObject::SuperData::link<QDBusAbstractAdaptor::staticMetaObject>(),
    qt_meta_stringdata_CLASSLauncher1AdaptorENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSLauncher1AdaptorENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSLauncher1AdaptorENDCLASS_t,
        // property 'Visible'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<Launcher1Adaptor, std::true_type>,
        // method 'Closed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'Shown'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'VisibleChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'Exit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'Hide'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'Show'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'ShowByMode'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qlonglong, std::false_type>,
        // method 'Toggle'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void Launcher1Adaptor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<Launcher1Adaptor *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->Closed(); break;
        case 1: _t->Shown(); break;
        case 2: _t->VisibleChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 3: _t->Exit(); break;
        case 4: _t->Hide(); break;
        case 5: _t->Show(); break;
        case 6: _t->ShowByMode((*reinterpret_cast< std::add_pointer_t<qlonglong>>(_a[1]))); break;
        case 7: _t->Toggle(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (Launcher1Adaptor::*)();
            if (_t _q_method = &Launcher1Adaptor::Closed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (Launcher1Adaptor::*)();
            if (_t _q_method = &Launcher1Adaptor::Shown; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (Launcher1Adaptor::*)(bool );
            if (_t _q_method = &Launcher1Adaptor::VisibleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<Launcher1Adaptor *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< bool*>(_v) = _t->visible(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *Launcher1Adaptor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Launcher1Adaptor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSLauncher1AdaptorENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDBusAbstractAdaptor::qt_metacast(_clname);
}

int Launcher1Adaptor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDBusAbstractAdaptor::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 8;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    }
    return _id;
}

// SIGNAL 0
void Launcher1Adaptor::Closed()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void Launcher1Adaptor::Shown()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void Launcher1Adaptor::VisibleChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
