
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "" "src/quick/launcher-qml-utils_autogen/timestamp" "custom" "src/quick/launcher-qml-utils_autogen/deps"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp" "src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o" "gcc" "src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o" "gcc" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o" "gcc" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o" "gcc" "src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
