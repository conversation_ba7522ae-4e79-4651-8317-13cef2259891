# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/quick/CMakeFiles/launcher-qml-utils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.make

# Include the progress variables for this target.
include src/quick/CMakeFiles/launcher-qml-utils.dir/progress.make

# Include the compile flags for this target's objects.
include src/quick/CMakeFiles/launcher-qml-utils.dir/flags.make

src/quick/launcher-qml-utils_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/quick/launcher-qml-utils_autogen/timestamp: src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target launcher-qml-utils"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/CMakeFiles/launcher-qml-utils_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/timestamp

src/quick/CMakeFiles/launcher-qml-utils.dir/codegen:
.PHONY : src/quick/CMakeFiles/launcher-qml-utils.dir/codegen

src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/flags.make
src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o: src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp
src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o -MF CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp

src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp > CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.i

src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp -o CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.s

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/flags.make
src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp
src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o -MF CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o.d -o CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp > CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.i

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp -o CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.s

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/flags.make
src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp
src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o -MF CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o.d -o CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp > CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.i

src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp -o CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.s

src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/flags.make
src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp
src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o: src/quick/CMakeFiles/launcher-qml-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o -MF CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o.d -o CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp

src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp > CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.i

src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp -o CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.s

launcher-qml-utils: src/quick/CMakeFiles/launcher-qml-utils.dir/launcher-qml-utils_autogen/mocs_compilation.cpp.o
launcher-qml-utils: src/quick/CMakeFiles/launcher-qml-utils.dir/launcherappiconprovider.cpp.o
launcher-qml-utils: src/quick/CMakeFiles/launcher-qml-utils.dir/launcherfoldericonprovider.cpp.o
launcher-qml-utils: src/quick/CMakeFiles/launcher-qml-utils.dir/blurhashimageprovider.cpp.o
launcher-qml-utils: src/quick/CMakeFiles/launcher-qml-utils.dir/build.make
.PHONY : launcher-qml-utils

# Rule to build all files generated by this target.
src/quick/CMakeFiles/launcher-qml-utils.dir/build: launcher-qml-utils
.PHONY : src/quick/CMakeFiles/launcher-qml-utils.dir/build

src/quick/CMakeFiles/launcher-qml-utils.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick && $(CMAKE_COMMAND) -P CMakeFiles/launcher-qml-utils.dir/cmake_clean.cmake
.PHONY : src/quick/CMakeFiles/launcher-qml-utils.dir/clean

src/quick/CMakeFiles/launcher-qml-utils.dir/depend: src/quick/launcher-qml-utils_autogen/timestamp
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/quick /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/CMakeFiles/launcher-qml-utils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/quick/CMakeFiles/launcher-qml-utils.dir/depend

