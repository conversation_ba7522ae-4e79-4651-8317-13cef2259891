# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/include -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils -isystem /usr/include/x86_64-linux-gnu/qt6/QtQuick -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtQml -isystem /usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration -isystem /usr/include/x86_64-linux-gnu/qt6/QtNetwork -isystem /usr/include/x86_64-linux-gnu/qt6/QtQmlMeta -isystem /usr/include/x86_64-linux-gnu/qt6/QtQmlModels -isystem /usr/include/x86_64-linux-gnu/qt6/QtQmlWorkerScript -isystem /usr/include/x86_64-linux-gnu/qt6/QtOpenGL

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC

