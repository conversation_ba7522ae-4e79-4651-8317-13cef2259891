{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigExtras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtInstallPaths.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeatureCommon.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicAppleHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicGitHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicPluginHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTargetHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTestHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicToolHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/deps", "DEP_FILE_RULE_NAME": "launcher-qml-utils_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.h", "Mu", "EWIEGA46WW/moc_blurhashimageprovider.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.h", "Mu", "EWIEGA46WW/moc_launcherappiconprovider.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.h", "Mu", "EWIEGA46WW/moc_launcherfoldericonprovider.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "QT_CORE_LIB", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils", "/usr/include/x86_64-linux-gnu/qt6/QtQuick", "/usr/include/x86_64-linux-gnu/qt6", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtQml", "/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration", "/usr/include/x86_64-linux-gnu/qt6/QtNetwork", "/usr/include/x86_64-linux-gnu/qt6/QtQmlMeta", "/usr/include/x86_64-linux-gnu/qt6/QtQmlModels", "/usr/include/x86_64-linux-gnu/qt6/QtQmlWorkerScript", "/usr/include/x86_64-linux-gnu/qt6/QtOpenGL", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/launcher-qml-utils_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/CMakeFiles/launcher-qml-utils_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/quick/CMakeFiles/launcher-qml-utils_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/blurhashimageprovider.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherappiconprovider.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/quick/launcherfoldericonprovider.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}