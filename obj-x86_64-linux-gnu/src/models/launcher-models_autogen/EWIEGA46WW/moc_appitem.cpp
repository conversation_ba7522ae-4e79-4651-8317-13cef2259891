/****************************************************************************
** Meta object code from reading C++ file 'appitem.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/appitem.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'appitem.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAppItemENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAppItemENDCLASS = QtMocHelpers::stringData(
    "AppItem",
    "QML.Element",
    "QML.Creatable",
    "false",
    "QML.UncreatableReason",
    "AppItem should only be created from C++ side",
    "Roles",
    "DesktopIdRole",
    "Categories",
    "DDECategoryRole",
    "IconNameRole",
    "NameRole",
    "InstalledTimeRole",
    "LastLaunchedTimeRole",
    "LaunchedTimesRole",
    "IsAutoStartRole",
    "VendorRole",
    "GenericNameRole",
    "ModelExtendedRole",
    "DDECategories",
    "Internet",
    "Chat",
    "Music",
    "Video",
    "Graphics",
    "Game",
    "Office",
    "Reading",
    "Development",
    "System",
    "Others"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAppItemENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       3,   14, // classinfo
       0,    0, // methods
       0,    0, // properties
       2,   20, // enums/sets
       0,    0, // constructors
       4,       // flags
       0,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,
       4,    5,

 // enums: name, alias, flags, count, data
       6,    6, 0x0,   12,   30,
      19,   19, 0x0,   11,   54,

 // enum data: key, value
       7, uint(AppItem::DesktopIdRole),
       8, uint(AppItem::Categories),
       9, uint(AppItem::DDECategoryRole),
      10, uint(AppItem::IconNameRole),
      11, uint(AppItem::NameRole),
      12, uint(AppItem::InstalledTimeRole),
      13, uint(AppItem::LastLaunchedTimeRole),
      14, uint(AppItem::LaunchedTimesRole),
      15, uint(AppItem::IsAutoStartRole),
      16, uint(AppItem::VendorRole),
      17, uint(AppItem::GenericNameRole),
      18, uint(AppItem::ModelExtendedRole),
      20, uint(AppItem::Internet),
      21, uint(AppItem::Chat),
      22, uint(AppItem::Music),
      23, uint(AppItem::Video),
      24, uint(AppItem::Graphics),
      25, uint(AppItem::Game),
      26, uint(AppItem::Office),
      27, uint(AppItem::Reading),
      28, uint(AppItem::Development),
      29, uint(AppItem::System),
      30, uint(AppItem::Others),

       0        // eod
};

Q_CONSTINIT const QMetaObject AppItem::staticMetaObject = { {
    QtPrivate::MetaObjectForType<QStandardItem>::value,
    qt_meta_stringdata_CLASSAppItemENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAppItemENDCLASS,
    nullptr,
    nullptr,
    qt_metaTypeArray<
        // enum 'Roles'
        AppItem::Roles,
        // enum 'DDECategories'
        AppItem::DDECategories,
        // Q_OBJECT / Q_GADGET
        AppItem
    >,
    nullptr
} };

QT_WARNING_POP
