{"classes": [{"classInfos": [{"name": "QML.Element", "value": "FavoritedProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "FavoritedProxyModel", "lineNumber": 10, "methods": [{"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 0, "name": "exists", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 1, "name": "addFavorite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 2, "name": "removeFavorite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 3, "name": "pinToTop", "returnType": "void"}], "object": true, "qualifiedClassName": "FavoritedProxyModel", "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "favoritedproxymodel.h", "outputRevision": 68}