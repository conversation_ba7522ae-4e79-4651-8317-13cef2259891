/****************************************************************************
** Meta object code from reading C++ file 'itemarrangementproxymodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/itemarrangementproxymodel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'itemarrangementproxymodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSItemArrangementProxyModelENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSItemArrangementProxyModelENDCLASS = QtMocHelpers::stringData(
    "ItemArrangementProxyModel",
    "QML.Element",
    "QML.Singleton",
    "true",
    "topLevelPageCountChanged",
    "",
    "folderPageCountChanged",
    "folderId",
    "pageCount",
    "updateFolderName",
    "name",
    "bringToFront",
    "id",
    "commitDndOperation",
    "dragId",
    "dropId",
    "DndOperation",
    "op",
    "pageHint",
    "creatEmptyPage",
    "removeEmptyPage",
    "ItemType",
    "AppItemType",
    "FolderItemType",
    "Roles",
    "PageRole",
    "IndexInPageRole",
    "FolderIdNumberRole",
    "IconsNameRole",
    "ItemTypeRole",
    "DndPrepend",
    "DndJoin",
    "DndAppend"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSItemArrangementProxyModelENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
      11,   18, // methods
       0,    0, // properties
       3,  121, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   84,    5, 0x06,    4 /* Public */,
       6,    1,   85,    5, 0x06,    5 /* Public */,

 // methods: name, argc, parameters, tag, flags, initial metatype offsets
       8,    1,   88,    5, 0x102,    7 /* Public | MethodIsConst  */,
       8,    0,   91,    5, 0x122,    9 /* Public | MethodCloned | MethodIsConst  */,
       9,    2,   92,    5, 0x02,   10 /* Public */,
      11,    1,   97,    5, 0x02,   13 /* Public */,
      13,    4,  100,    5, 0x02,   15 /* Public */,
      13,    3,  109,    5, 0x22,   20 /* Public | MethodCloned */,
      19,    1,  116,    5, 0x102,   24 /* Public | MethodIsConst  */,
      19,    0,  119,    5, 0x122,   26 /* Public | MethodCloned | MethodIsConst  */,
      20,    0,  120,    5, 0x102,   27 /* Public | MethodIsConst  */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    7,

 // methods: parameters
    QMetaType::Int, QMetaType::Int,    7,
    QMetaType::Int,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    7,   10,
    QMetaType::Void, QMetaType::QString,   12,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, 0x80000000 | 16, QMetaType::Int,   14,   15,   17,   18,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, 0x80000000 | 16,   14,   15,   17,
    QMetaType::Int, QMetaType::Int,    7,
    QMetaType::Int,
    QMetaType::Void,

 // enums: name, alias, flags, count, data
      21,   21, 0x0,    2,  136,
      24,   24, 0x0,    5,  140,
      16,   16, 0x0,    3,  150,

 // enum data: key, value
      22, uint(ItemArrangementProxyModel::AppItemType),
      23, uint(ItemArrangementProxyModel::FolderItemType),
      25, uint(ItemArrangementProxyModel::PageRole),
      26, uint(ItemArrangementProxyModel::IndexInPageRole),
      27, uint(ItemArrangementProxyModel::FolderIdNumberRole),
      28, uint(ItemArrangementProxyModel::IconsNameRole),
      29, uint(ItemArrangementProxyModel::ItemTypeRole),
      30, uint(ItemArrangementProxyModel::DndPrepend),
      31, uint(ItemArrangementProxyModel::DndJoin),
      32, uint(ItemArrangementProxyModel::DndAppend),

       0        // eod
};

Q_CONSTINIT const QMetaObject ItemArrangementProxyModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QConcatenateTablesProxyModel::staticMetaObject>(),
    qt_meta_stringdata_CLASSItemArrangementProxyModelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSItemArrangementProxyModelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // enum 'ItemType'
        ItemArrangementProxyModel::ItemType,
        // enum 'Roles'
        ItemArrangementProxyModel::Roles,
        // enum 'DndOperation'
        ItemArrangementProxyModel::DndOperation,
        // Q_OBJECT / Q_GADGET
        ItemArrangementProxyModel,
        // method 'topLevelPageCountChanged'
        void,
        // method 'folderPageCountChanged'
        void,
        int,
        // method 'pageCount'
        int,
        int,
        // method 'pageCount'
        int,
        // method 'updateFolderName'
        void,
        int,
        const QString &,
        // method 'bringToFront'
        void,
        const QString &,
        // method 'commitDndOperation'
        void,
        const QString &,
        const QString &,
        const DndOperation,
        int,
        // method 'commitDndOperation'
        void,
        const QString &,
        const QString &,
        const DndOperation,
        // method 'creatEmptyPage'
        int,
        int,
        // method 'creatEmptyPage'
        int,
        // method 'removeEmptyPage'
        void
    >,
    nullptr
} };

void ItemArrangementProxyModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ItemArrangementProxyModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->topLevelPageCountChanged(); break;
        case 1: _t->folderPageCountChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: { int _r = _t->pageCount((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 3: { int _r = _t->pageCount();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 4: _t->updateFolderName((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->bringToFront((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->commitDndOperation((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<DndOperation>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 7: _t->commitDndOperation((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<DndOperation>>(_a[3]))); break;
        case 8: { int _r = _t->creatEmptyPage((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 9: { int _r = _t->creatEmptyPage();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 10: _t->removeEmptyPage(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ItemArrangementProxyModel::*)();
            if (_t _q_method = &ItemArrangementProxyModel::topLevelPageCountChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ItemArrangementProxyModel::*)(int );
            if (_t _q_method = &ItemArrangementProxyModel::folderPageCountChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
    }
}

const QMetaObject *ItemArrangementProxyModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ItemArrangementProxyModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSItemArrangementProxyModelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QConcatenateTablesProxyModel::qt_metacast(_clname);
}

int ItemArrangementProxyModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QConcatenateTablesProxyModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void ItemArrangementProxyModel::topLevelPageCountChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ItemArrangementProxyModel::folderPageCountChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
