{"classes": [{"classInfos": [{"name": "QML.Element", "value": "CountLimitProxyModel"}], "className": "CountLimitProxyModel", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "maxRowCount", "notify": "maxRowCountChanged", "read": "maxRowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxRowCount"}], "qualifiedClassName": "CountLimitProxyModel", "signals": [{"access": "public", "index": 0, "name": "maxRowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "countlimitproxymodel.h", "outputRevision": 68}