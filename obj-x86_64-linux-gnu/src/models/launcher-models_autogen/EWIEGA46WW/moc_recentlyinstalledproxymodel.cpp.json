{"classes": [{"classInfos": [{"name": "QML.Element", "value": "RecentlyInstalledProxyModel"}], "className": "RecentlyInstalledProxyModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "m_lastLaunchedTimeRole", "name": "lastLaunchedTimeRole", "notify": "lastLaunchedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_installedTimeRole", "name": "installedTimeRole", "notify": "installedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "RecentlyInstalledProxyModel", "signals": [{"access": "public", "index": 0, "name": "lastLaunchedTimeRoleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "installedTimeRoleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "recentlyinstalledproxymodel.h", "outputRevision": 68}