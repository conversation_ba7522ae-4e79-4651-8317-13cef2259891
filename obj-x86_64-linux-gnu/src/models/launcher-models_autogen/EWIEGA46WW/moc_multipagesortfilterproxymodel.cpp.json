{"classes": [{"classInfos": [{"name": "QML.Element", "value": "MultipageSortFilterProxyModel"}], "className": "MultipageSortFilterProxyModel", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceModel", "notify": "sourceModelChanged", "read": "sourceModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_folderId", "name": "folderId", "notify": "onFolderIdChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_pageId", "name": "pageId", "notify": "onPageIdChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_filterOnlyMode", "name": "filterOnlyMode", "notify": "onFilterOnlyModeChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "MultipageSortFilterProxyModel", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "onFolderIdChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "onPageIdChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QObject*"}], "index": 2, "name": "sourceModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "onFilterOnlyModeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "multipagesortfilterproxymodel.h", "outputRevision": 68}