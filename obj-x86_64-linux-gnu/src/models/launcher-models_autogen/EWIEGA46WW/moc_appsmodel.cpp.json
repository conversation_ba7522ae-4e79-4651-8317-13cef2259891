{"classes": [{"classInfos": [{"name": "QML.Element", "value": "AppsModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "AppsModel", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["TransliteratedRole", "AllTransliteratedRole", "NameRole", "ProxyModelExtendedRole"]}], "lineNumber": 20, "object": true, "qualifiedClassName": "AppsModel", "slots": [{"access": "private", "index": 0, "name": "updateModelData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}], "inputFile": "appsmodel.h", "outputRevision": 68}