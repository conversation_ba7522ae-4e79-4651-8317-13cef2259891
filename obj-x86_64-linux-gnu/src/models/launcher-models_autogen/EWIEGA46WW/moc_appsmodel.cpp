/****************************************************************************
** Meta object code from reading C++ file 'appsmodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/appsmodel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'appsmodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAppsModelENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAppsModelENDCLASS = QtMocHelpers::stringData(
    "AppsModel",
    "QML.Element",
    "QML.Singleton",
    "true",
    "updateModelData",
    "",
    "Roles",
    "TransliteratedRole",
    "AllTransliteratedRole",
    "NameRole",
    "ProxyModelExtendedRole"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAppsModelENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
       1,   18, // methods
       0,    0, // properties
       1,   25, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   24,    5, 0x08,    2 /* Private */,

 // slots: parameters
    QMetaType::Void,

 // enums: name, alias, flags, count, data
       6,    6, 0x0,    4,   30,

 // enum data: key, value
       7, uint(AppsModel::TransliteratedRole),
       8, uint(AppsModel::AllTransliteratedRole),
       9, uint(AppsModel::NameRole),
      10, uint(AppsModel::ProxyModelExtendedRole),

       0        // eod
};

Q_CONSTINIT const QMetaObject AppsModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QStandardItemModel::staticMetaObject>(),
    qt_meta_stringdata_CLASSAppsModelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAppsModelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // enum 'Roles'
        AppsModel::Roles,
        // Q_OBJECT / Q_GADGET
        AppsModel,
        // method 'updateModelData'
        void
    >,
    nullptr
} };

void AppsModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<AppsModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->updateModelData(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *AppsModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AppsModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAppsModelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QStandardItemModel::qt_metacast(_clname);
}

int AppsModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QStandardItemModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 1)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 1;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 1)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 1;
    }
    return _id;
}
QT_WARNING_POP
