{"classes": [{"classInfos": [{"name": "QML.Element", "value": "AppItem"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "AppItem should only be created from C++ side"}], "className": "AppItem", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["DesktopIdRole", "Categories", "DDECategoryRole", "IconNameRole", "NameRole", "InstalledTimeRole", "LastLaunchedTimeRole", "LaunchedTimesRole", "IsAutoStartRole", "VendorRole", "GenericNameRole", "ModelExtendedRole"]}, {"isClass": false, "isFlag": false, "name": "DDECategories", "values": ["Internet", "Cha<PERSON>", "Music", "Video", "Graphics", "Game", "Office", "Reading", "Development", "System", "Others"]}], "gadget": true, "lineNumber": 10, "qualifiedClassName": "AppItem", "superClasses": [{"access": "public", "name": "QStandardItem"}]}], "inputFile": "appitem.h", "outputRevision": 68}