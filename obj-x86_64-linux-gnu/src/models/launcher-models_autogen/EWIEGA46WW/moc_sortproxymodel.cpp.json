{"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "SortProxyModel", "lineNumber": 35, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sortRole", "notify": "sortRoleChanged", "read": "sortRole", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSortRole"}], "qualifiedClassName": "SortProxyModel", "signals": [{"access": "public", "index": 0, "name": "sortRoleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "sortCaseSensitivityChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "sortColumnChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "sortOrderChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 4, "name": "resetInternalData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractProxyModel"}]}], "inputFile": "sortproxymodel.h", "outputRevision": 68}