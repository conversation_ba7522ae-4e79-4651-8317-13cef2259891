/****************************************************************************
** Meta object code from reading C++ file 'recentlyinstalledproxymodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/recentlyinstalledproxymodel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'recentlyinstalledproxymodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSRecentlyInstalledProxyModelENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSRecentlyInstalledProxyModelENDCLASS = QtMocHelpers::stringData(
    "RecentlyInstalledProxyModel",
    "QML.Element",
    "lastLaunchedTimeRoleChanged",
    "",
    "installedTimeRoleChanged",
    "lastLaunchedTimeRole",
    "installedTimeRole"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSRecentlyInstalledProxyModelENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       1,   14, // classinfo
       2,   16, // methods
       2,   30, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // classinfo: key, value
       1,    0,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       2,    0,   28,    3, 0x06,    3 /* Public */,
       4,    0,   29,    3, 0x06,    4 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,

 // properties: name, type, flags, notifyId, revision
       5, QMetaType::Int, 0x00015003, uint(0), 0,
       6, QMetaType::Int, 0x00015003, uint(1), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject RecentlyInstalledProxyModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QSortFilterProxyModel::staticMetaObject>(),
    qt_meta_stringdata_CLASSRecentlyInstalledProxyModelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSRecentlyInstalledProxyModelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'lastLaunchedTimeRole'
        int,
        // property 'installedTimeRole'
        int,
        // Q_OBJECT / Q_GADGET
        RecentlyInstalledProxyModel,
        // method 'lastLaunchedTimeRoleChanged'
        void,
        // method 'installedTimeRoleChanged'
        void
    >,
    nullptr
} };

void RecentlyInstalledProxyModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<RecentlyInstalledProxyModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->lastLaunchedTimeRoleChanged(); break;
        case 1: _t->installedTimeRoleChanged(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (RecentlyInstalledProxyModel::*)();
            if (_t _q_method = &RecentlyInstalledProxyModel::lastLaunchedTimeRoleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (RecentlyInstalledProxyModel::*)();
            if (_t _q_method = &RecentlyInstalledProxyModel::installedTimeRoleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<RecentlyInstalledProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< int*>(_v) = _t->m_lastLaunchedTimeRole; break;
        case 1: *reinterpret_cast< int*>(_v) = _t->m_installedTimeRole; break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<RecentlyInstalledProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0:
            if (_t->m_lastLaunchedTimeRole != *reinterpret_cast< int*>(_v)) {
                _t->m_lastLaunchedTimeRole = *reinterpret_cast< int*>(_v);
                Q_EMIT _t->lastLaunchedTimeRoleChanged();
            }
            break;
        case 1:
            if (_t->m_installedTimeRole != *reinterpret_cast< int*>(_v)) {
                _t->m_installedTimeRole = *reinterpret_cast< int*>(_v);
                Q_EMIT _t->installedTimeRoleChanged();
            }
            break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
    (void)_a;
}

const QMetaObject *RecentlyInstalledProxyModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RecentlyInstalledProxyModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSRecentlyInstalledProxyModelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QQmlParserStatus"))
        return static_cast< QQmlParserStatus*>(this);
    if (!strcmp(_clname, "org.qt-project.Qt.QQmlParserStatus"))
        return static_cast< QQmlParserStatus*>(this);
    return QSortFilterProxyModel::qt_metacast(_clname);
}

int RecentlyInstalledProxyModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QSortFilterProxyModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 2;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void RecentlyInstalledProxyModel::lastLaunchedTimeRoleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void RecentlyInstalledProxyModel::installedTimeRoleChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
QT_WARNING_POP
