/****************************************************************************
** Meta object code from reading C++ file 'categorizedsortproxymodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/categorizedsortproxymodel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'categorizedsortproxymodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSCategorizedSortProxyModelENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSCategorizedSortProxyModelENDCLASS = QtMocHelpers::stringData(
    "CategorizedSortProxyModel",
    "QML.Element",
    "QML.Singleton",
    "true",
    "categoryTypeChanged",
    "",
    "alphabetarySections",
    "DDECategorySections",
    "QList<int>",
    "categoryType",
    "CategoryType",
    "sortRoleName",
    "Alphabetary",
    "DDECategory",
    "FreeCategory"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSCategorizedSortProxyModelENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       2,   14, // classinfo
       3,   18, // methods
       2,   39, // properties
       1,   49, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // classinfo: key, value
       1,    0,
       2,    3,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       4,    0,   36,    5, 0x06,    4 /* Public */,

 // methods: name, argc, parameters, tag, flags, initial metatype offsets
       6,    0,   37,    5, 0x102,    5 /* Public | MethodIsConst  */,
       7,    0,   38,    5, 0x102,    6 /* Public | MethodIsConst  */,

 // signals: parameters
    QMetaType::Void,

 // methods: parameters
    QMetaType::QStringList,
    0x80000000 | 8,

 // properties: name, type, flags, notifyId, revision
       9, 0x80000000 | 10, 0x0001510b, uint(0), 0,
      11, QMetaType::QString, 0x00015001, uint(0), 0,

 // enums: name, alias, flags, count, data
      10,   10, 0x0,    3,   54,

 // enum data: key, value
      12, uint(CategorizedSortProxyModel::Alphabetary),
      13, uint(CategorizedSortProxyModel::DDECategory),
      14, uint(CategorizedSortProxyModel::FreeCategory),

       0        // eod
};

Q_CONSTINIT const QMetaObject CategorizedSortProxyModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QSortFilterProxyModel::staticMetaObject>(),
    qt_meta_stringdata_CLASSCategorizedSortProxyModelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSCategorizedSortProxyModelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'categoryType'
        CategoryType,
        // property 'sortRoleName'
        QString,
        // enum 'CategoryType'
        CategorizedSortProxyModel::CategoryType,
        // Q_OBJECT / Q_GADGET
        CategorizedSortProxyModel,
        // method 'categoryTypeChanged'
        void,
        // method 'alphabetarySections'
        QList<QString>,
        // method 'DDECategorySections'
        QList<int>
    >,
    nullptr
} };

void CategorizedSortProxyModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CategorizedSortProxyModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->categoryTypeChanged(); break;
        case 1: { QList<QString> _r = _t->alphabetarySections();
            if (_a[0]) *reinterpret_cast< QList<QString>*>(_a[0]) = std::move(_r); }  break;
        case 2: { QList<int> _r = _t->DDECategorySections();
            if (_a[0]) *reinterpret_cast< QList<int>*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CategorizedSortProxyModel::*)();
            if (_t _q_method = &CategorizedSortProxyModel::categoryTypeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<CategorizedSortProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< CategoryType*>(_v) = _t->categoryType(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->sortRoleName(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<CategorizedSortProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setCategoryType(*reinterpret_cast< CategoryType*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *CategorizedSortProxyModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CategorizedSortProxyModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSCategorizedSortProxyModelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QSortFilterProxyModel::qt_metacast(_clname);
}

int CategorizedSortProxyModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QSortFilterProxyModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void CategorizedSortProxyModel::categoryTypeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}
QT_WARNING_POP
