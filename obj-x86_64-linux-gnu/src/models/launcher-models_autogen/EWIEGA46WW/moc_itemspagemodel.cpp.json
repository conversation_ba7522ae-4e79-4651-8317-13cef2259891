{"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemsPageModel"}], "className": "ItemsPageModel", "lineNumber": 13, "methods": [{"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 1, "name": "rowCount", "returnType": "int"}, {"access": "public", "index": 2, "isCloned": true, "name": "rowCount", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceModel", "notify": "sourceModelChanged", "read": "sourceModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setSourceModel"}], "qualifiedClassName": "ItemsPageModel", "signals": [{"access": "public", "arguments": [{"type": "QObject*"}], "index": 0, "name": "sourceModelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}]}], "inputFile": "itemspagemodel.h", "outputRevision": 68}