{"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemArrangementProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "ItemArrangementProxyModel", "enums": [{"isClass": false, "isFlag": false, "name": "ItemType", "values": ["AppItemType", "FolderItemType"]}, {"isClass": false, "isFlag": false, "name": "Roles", "values": ["PageRole", "IndexInPageRole", "FolderIdNumberRole", "IconsNameRole", "ItemTypeRole"]}, {"isClass": false, "isFlag": false, "name": "DndOperation", "values": ["DndPrepend", "DndJoin", "DndAppend"]}], "lineNumber": 14, "methods": [{"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 2, "name": "pageCount", "returnType": "int"}, {"access": "public", "index": 3, "isCloned": true, "name": "pageCount", "returnType": "int"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}, {"name": "name", "type": "QString"}], "index": 4, "name": "updateFolderName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 5, "name": "bringToFront", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dragId", "type": "QString"}, {"name": "dropId", "type": "QString"}, {"name": "op", "type": "DndOperation"}, {"name": "pageHint", "type": "int"}], "index": 6, "name": "commitDndOperation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dragId", "type": "QString"}, {"name": "dropId", "type": "QString"}, {"name": "op", "type": "DndOperation"}], "index": 7, "isCloned": true, "name": "commitDndOperation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 8, "name": "creatEmptyPage", "returnType": "int"}, {"access": "public", "index": 9, "isCloned": true, "name": "creatEmptyPage", "returnType": "int"}, {"access": "public", "index": 10, "name": "removeEmptyPage", "returnType": "void"}], "object": true, "qualifiedClassName": "ItemArrangementProxyModel", "signals": [{"access": "public", "index": 0, "name": "topLevelPageCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 1, "name": "folderPageCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QConcatenateTablesProxyModel"}]}], "inputFile": "itemarrangementproxymodel.h", "outputRevision": 68}