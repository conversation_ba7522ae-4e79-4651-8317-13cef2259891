{"classes": [{"classInfos": [{"name": "QML.Element", "value": "FrequentlyUsedProxyModel"}], "className": "FrequentlyUsedProxyModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "recentlyInstalledModel", "notify": "recentlyInstalledModelChanged", "read": "recentlyInstalledModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setRecentlyInstalledModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_desktopIdRole", "name": "desktopIdRole", "notify": "desktopIdRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_launchedTimesRole", "name": "launchedTimesRole", "notify": "launchedTimesRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_lastLaunchedTimeRole", "name": "lastLaunchedTimeRole", "notify": "lastLaunchedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "FrequentlyUsedProxyModel", "signals": [{"access": "public", "index": 0, "name": "recentlyInstalledModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "desktopIdRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 2, "name": "launchedTimesRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 3, "name": "lastLaunchedTimeRoleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "frequentlyusedproxymodel.h", "outputRevision": 68}