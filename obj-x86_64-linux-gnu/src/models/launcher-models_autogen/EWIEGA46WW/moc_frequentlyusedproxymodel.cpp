/****************************************************************************
** Meta object code from reading C++ file 'frequentlyusedproxymodel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/frequentlyusedproxymodel.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'frequentlyusedproxymodel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSFrequentlyUsedProxyModelENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSFrequentlyUsedProxyModelENDCLASS = QtMocHelpers::stringData(
    "FrequentlyUsedProxyModel",
    "QML.Element",
    "recentlyInstalledModelChanged",
    "",
    "desktopIdRoleChanged",
    "launchedTimesRoleChanged",
    "lastLaunchedTimeRoleChanged",
    "recentlyInstalledModel",
    "QAbstractItemModel*",
    "desktopIdRole",
    "launchedTimesRole",
    "lastLaunchedTimeRole"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSFrequentlyUsedProxyModelENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       1,   14, // classinfo
       4,   16, // methods
       4,   50, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // classinfo: key, value
       1,    0,

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       2,    0,   40,    3, 0x06,    5 /* Public */,
       4,    1,   41,    3, 0x06,    6 /* Public */,
       5,    1,   44,    3, 0x06,    8 /* Public */,
       6,    1,   47,    3, 0x06,   10 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int,    3,

 // properties: name, type, flags, notifyId, revision
       7, 0x80000000 | 8, 0x0001590b, uint(0), 0,
       9, QMetaType::Int, 0x00015003, uint(1), 0,
      10, QMetaType::Int, 0x00015003, uint(2), 0,
      11, QMetaType::Int, 0x00015003, uint(3), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject FrequentlyUsedProxyModel::staticMetaObject = { {
    QMetaObject::SuperData::link<QSortFilterProxyModel::staticMetaObject>(),
    qt_meta_stringdata_CLASSFrequentlyUsedProxyModelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSFrequentlyUsedProxyModelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_metaTypeArray<
        // property 'recentlyInstalledModel'
        QAbstractItemModel*,
        // property 'desktopIdRole'
        int,
        // property 'launchedTimesRole'
        int,
        // property 'lastLaunchedTimeRole'
        int,
        // Q_OBJECT / Q_GADGET
        FrequentlyUsedProxyModel,
        // method 'recentlyInstalledModelChanged'
        void,
        // method 'desktopIdRoleChanged'
        void,
        int,
        // method 'launchedTimesRoleChanged'
        void,
        int,
        // method 'lastLaunchedTimeRoleChanged'
        void,
        int
    >,
    nullptr
} };

void FrequentlyUsedProxyModel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<FrequentlyUsedProxyModel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->recentlyInstalledModelChanged(); break;
        case 1: _t->desktopIdRoleChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->launchedTimesRoleChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 3: _t->lastLaunchedTimeRoleChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (FrequentlyUsedProxyModel::*)();
            if (_t _q_method = &FrequentlyUsedProxyModel::recentlyInstalledModelChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (FrequentlyUsedProxyModel::*)(int );
            if (_t _q_method = &FrequentlyUsedProxyModel::desktopIdRoleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (FrequentlyUsedProxyModel::*)(int );
            if (_t _q_method = &FrequentlyUsedProxyModel::launchedTimesRoleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (FrequentlyUsedProxyModel::*)(int );
            if (_t _q_method = &FrequentlyUsedProxyModel::lastLaunchedTimeRoleChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractItemModel* >(); break;
        }
    }  else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<FrequentlyUsedProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QAbstractItemModel**>(_v) = _t->recentlyInstalledModel(); break;
        case 1: *reinterpret_cast< int*>(_v) = _t->m_desktopIdRole; break;
        case 2: *reinterpret_cast< int*>(_v) = _t->m_launchedTimesRole; break;
        case 3: *reinterpret_cast< int*>(_v) = _t->m_lastLaunchedTimeRole; break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<FrequentlyUsedProxyModel *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setRecentlyInstalledModel(*reinterpret_cast< QAbstractItemModel**>(_v)); break;
        case 1:
            if (_t->m_desktopIdRole != *reinterpret_cast< int*>(_v)) {
                _t->m_desktopIdRole = *reinterpret_cast< int*>(_v);
                Q_EMIT _t->desktopIdRoleChanged(_t->m_desktopIdRole);
            }
            break;
        case 2:
            if (_t->m_launchedTimesRole != *reinterpret_cast< int*>(_v)) {
                _t->m_launchedTimesRole = *reinterpret_cast< int*>(_v);
                Q_EMIT _t->launchedTimesRoleChanged(_t->m_launchedTimesRole);
            }
            break;
        case 3:
            if (_t->m_lastLaunchedTimeRole != *reinterpret_cast< int*>(_v)) {
                _t->m_lastLaunchedTimeRole = *reinterpret_cast< int*>(_v);
                Q_EMIT _t->lastLaunchedTimeRoleChanged(_t->m_lastLaunchedTimeRole);
            }
            break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *FrequentlyUsedProxyModel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *FrequentlyUsedProxyModel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSFrequentlyUsedProxyModelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QQmlParserStatus"))
        return static_cast< QQmlParserStatus*>(this);
    if (!strcmp(_clname, "org.qt-project.Qt.QQmlParserStatus"))
        return static_cast< QQmlParserStatus*>(this);
    return QSortFilterProxyModel::qt_metacast(_clname);
}

int FrequentlyUsedProxyModel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QSortFilterProxyModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void FrequentlyUsedProxyModel::recentlyInstalledModelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void FrequentlyUsedProxyModel::desktopIdRoleChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void FrequentlyUsedProxyModel::launchedTimesRoleChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void FrequentlyUsedProxyModel::lastLaunchedTimeRoleChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
