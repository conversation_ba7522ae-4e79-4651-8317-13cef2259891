{"classes": [{"classInfos": [{"name": "QML.Element", "value": "CategorizedSortProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "CategorizedSortProxyModel", "enums": [{"isClass": false, "isFlag": false, "name": "CategoryType", "values": ["Alphabetary", "DDECategory", "FreeCategory"]}], "lineNumber": 10, "methods": [{"access": "public", "index": 1, "name": "alphabetarySections", "returnType": "QList<QString>"}, {"access": "public", "index": 2, "name": "DDECategorySections", "returnType": "QList<int>"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "categoryType", "notify": "categoryTypeChanged", "read": "categoryType", "required": false, "scriptable": true, "stored": true, "type": "CategoryType", "user": false, "write": "setCategoryType"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sortRoleName", "notify": "categoryTypeChanged", "read": "sortRoleName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "CategorizedSortProxyModel", "signals": [{"access": "public", "index": 0, "name": "categoryTypeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "categorizedsortproxymodel.h", "outputRevision": 68}