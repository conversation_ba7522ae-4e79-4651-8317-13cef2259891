{"classes": [{"className": "ItemsPage", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pageCount", "notify": "pageCountChanged", "read": "pageCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "maxItemCountPerPage", "read": "maxItemCountPerPage", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "ItemsPage", "signals": [{"access": "public", "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pageCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 2, "name": "sigPageAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 3, "name": "sigPageRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "itemspage.h", "outputRevision": 68}