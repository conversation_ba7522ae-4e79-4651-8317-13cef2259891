file(REMOVE_RECURSE
  ".qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp"
  "CMakeFiles/launcher-models_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/launcher-models_autogen.dir/ParseCache.txt"
  "launcher-models_autogen"
  "CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o"
  "CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o.d"
  "CMakeFiles/launcher-models.dir/appitem.cpp.o"
  "CMakeFiles/launcher-models.dir/appitem.cpp.o.d"
  "CMakeFiles/launcher-models.dir/appsmodel.cpp.o"
  "CMakeFiles/launcher-models.dir/appsmodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/itemspage.cpp.o"
  "CMakeFiles/launcher-models.dir/itemspage.cpp.o.d"
  "CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o"
  "CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o"
  "CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o.d"
  "CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o"
  "CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o.d"
  "CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o"
  "CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o.d"
  "CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o.d"
  "CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o"
  "CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o.d"
  "launcher-models.qmltypes"
  "launcher-models_autogen/mocs_compilation.cpp"
  "launcher-models_autogen/timestamp"
  "launcher-models_qmltyperegistrations.cpp"
  "liblauncher-models.a"
  "liblauncher-models.pdb"
  "meta_types/launcher-models_json_file_list.txt"
  "meta_types/launcher-models_json_file_list.txt.timestamp"
  "meta_types/qt6launcher-models_none_metatypes.json"
  "meta_types/qt6launcher-models_none_metatypes.json.gen"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/launcher-models.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
