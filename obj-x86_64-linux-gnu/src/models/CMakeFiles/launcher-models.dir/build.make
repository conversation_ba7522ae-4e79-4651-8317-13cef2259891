# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/models/CMakeFiles/launcher-models.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/models/CMakeFiles/launcher-models.dir/compiler_depend.make

# Include the progress variables for this target.
include src/models/CMakeFiles/launcher-models.dir/progress.make

# Include the compile flags for this target's objects.
include src/models/CMakeFiles/launcher-models.dir/flags.make

src/models/meta_types/qt6launcher-models_none_metatypes.json.gen: /usr/lib/qt6/libexec/moc
src/models/meta_types/qt6launcher-models_none_metatypes.json.gen: src/models/meta_types/launcher-models_json_file_list.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running moc --collect-json for target launcher-models"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/lib/qt6/libexec/moc -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/qt6launcher-models_none_metatypes.json.gen --collect-json @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/launcher-models_json_file_list.txt
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E copy_if_different /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/qt6launcher-models_none_metatypes.json.gen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/qt6launcher-models_none_metatypes.json

src/models/launcher-models_qmltyperegistrations.cpp: src/models/qmltypes/launcher-models_foreign_types.txt
src/models/launcher-models_qmltyperegistrations.cpp: src/models/meta_types/qt6launcher-models_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/qt6/libexec/qmltyperegistrar
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6qml_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6core_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6network_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6gui_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6dbus_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6xml_none_metatypes.json
src/models/launcher-models_qmltyperegistrations.cpp: /usr/lib/x86_64-linux-gnu/qt6/metatypes/qt6concurrent_none_metatypes.json
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Automatic QML type registration for target launcher-models"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/lib/qt6/libexec/qmltyperegistrar --generate-qmltypes=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models.qmltypes --import-name=org.deepin.launchpad.models --major-version=1 --minor-version=0 @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/qmltypes/launcher-models_foreign_types.txt -o /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_qmltyperegistrations.cpp /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/qt6launcher-models_none_metatypes.json
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E make_directory /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/qmltypes
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/qmltypes/launcher-models.qmltypes

src/models/launcher-models.qmltypes: src/models/launcher-models_qmltyperegistrations.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/models/launcher-models.qmltypes

src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp: src/models/qmldir
src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp: src/models/.qt/rcc/qmake_org_deepin_launchpad_models.qrc
src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp: /usr/lib/qt6/libexec/rcc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Running rcc for resource qmake_org_deepin_launchpad_models"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/lib/qt6/libexec/rcc --output /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp --name qmake_org_deepin_launchpad_models /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qmake_org_deepin_launchpad_models.qrc

src/models/launcher-models_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/models/launcher-models_autogen/timestamp: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Automatic MOC for target launcher-models"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/timestamp

src/models/meta_types/launcher-models_json_file_list.txt: /usr/lib/qt6/libexec/cmake_automoc_parser
src/models/meta_types/launcher-models_json_file_list.txt: src/models/launcher-models_autogen/timestamp
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Running AUTOMOC file extraction for target launcher-models"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/lib/qt6/libexec/cmake_automoc_parser --cmake-autogen-cache-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models_autogen.dir/ParseCache.txt --cmake-autogen-info-file /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models_autogen.dir/AutogenInfo.json --output-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/launcher-models_json_file_list.txt --timestamp-file-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/meta_types/launcher-models_json_file_list.txt.timestamp --cmake-autogen-include-dir-path /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/include

src/models/meta_types/qt6launcher-models_none_metatypes.json: src/models/meta_types/qt6launcher-models_none_metatypes.json.gen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Generating meta_types/qt6launcher-models_none_metatypes.json"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/cmake -E true

src/models/CMakeFiles/launcher-models.dir/codegen:
.PHONY : src/models/CMakeFiles/launcher-models.dir/codegen

src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o: src/models/launcher-models_autogen/mocs_compilation.cpp
src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o -MF CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/mocs_compilation.cpp

src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/mocs_compilation.cpp > CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.i

src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/mocs_compilation.cpp -o CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.s

src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o: src/models/launcher-models_org_deepin_launchpad_modelsPlugin.cpp
src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o -MF CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o.d -o CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_org_deepin_launchpad_modelsPlugin.cpp

src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_org_deepin_launchpad_modelsPlugin.cpp > CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.i

src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_org_deepin_launchpad_modelsPlugin.cpp -o CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.s

src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o: src/models/launcher-models_qmltyperegistrations.cpp
src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o -MF CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o.d -o CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_qmltyperegistrations.cpp

src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_qmltyperegistrations.cpp > CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.i

src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_qmltyperegistrations.cpp -o CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.s

src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o: src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp
src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o -MF CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o.d -o CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp

src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp > CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.i

src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp -o CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.s

src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.cpp
src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o -MF CMakeFiles/launcher-models.dir/appsmodel.cpp.o.d -o CMakeFiles/launcher-models.dir/appsmodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.cpp

src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/appsmodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.cpp > CMakeFiles/launcher-models.dir/appsmodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/appsmodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.cpp -o CMakeFiles/launcher-models.dir/appsmodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.cpp
src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o -MF CMakeFiles/launcher-models.dir/appitem.cpp.o.d -o CMakeFiles/launcher-models.dir/appitem.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.cpp

src/models/CMakeFiles/launcher-models.dir/appitem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/appitem.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.cpp > CMakeFiles/launcher-models.dir/appitem.cpp.i

src/models/CMakeFiles/launcher-models.dir/appitem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/appitem.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.cpp -o CMakeFiles/launcher-models.dir/appitem.cpp.s

src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/sortproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.cpp > CMakeFiles/launcher-models.dir/sortproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/sortproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.cpp -o CMakeFiles/launcher-models.dir/sortproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.cpp > CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.cpp -o CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.cpp > CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.cpp -o CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.cpp > CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.cpp -o CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.cpp
src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o -MF CMakeFiles/launcher-models.dir/itemspage.cpp.o.d -o CMakeFiles/launcher-models.dir/itemspage.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.cpp

src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/itemspage.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.cpp > CMakeFiles/launcher-models.dir/itemspage.cpp.i

src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/itemspage.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.cpp -o CMakeFiles/launcher-models.dir/itemspage.cpp.s

src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.cpp > CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.cpp -o CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.cpp > CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.cpp -o CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.cpp > CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.cpp -o CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.cpp > CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.cpp -o CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.cpp > CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.cpp -o CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.cpp
src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o -MF CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o.d -o CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.cpp

src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.cpp > CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.cpp -o CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.s

src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/flags.make
src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.cpp
src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o: src/models/CMakeFiles/launcher-models.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o -MF CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o.d -o CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.cpp

src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-models.dir/itemspagemodel.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.cpp > CMakeFiles/launcher-models.dir/itemspagemodel.cpp.i

src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-models.dir/itemspagemodel.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.cpp -o CMakeFiles/launcher-models.dir/itemspagemodel.cpp.s

# Object files for target launcher-models
launcher__models_OBJECTS = \
"CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o" \
"CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o" \
"CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o" \
"CMakeFiles/launcher-models.dir/appsmodel.cpp.o" \
"CMakeFiles/launcher-models.dir/appitem.cpp.o" \
"CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/itemspage.cpp.o" \
"CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o" \
"CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o"

# External object files for target launcher-models
launcher__models_EXTERNAL_OBJECTS = \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o" \
"/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o"

src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/launcher-models_autogen/mocs_compilation.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/launcher-models_org_deepin_launchpad_modelsPlugin.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/launcher-models_qmltyperegistrations.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/appsmodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/appitem.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/sortproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/searchfilterproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/categorizedsortproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/favoritedproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/itemspage.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/itemarrangementproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/multipagesortfilterproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/recentlyinstalledproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/countlimitproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/freesortproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/frequentlyusedproxymodel.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/itemspagemodel.cpp.o
src/models/liblauncher-models.a: src/gioutils/CMakeFiles/gio-utils.dir/gio-utils_autogen/mocs_compilation.cpp.o
src/models/liblauncher-models.a: src/gioutils/CMakeFiles/gio-utils.dir/trashmonitor.cpp.o
src/models/liblauncher-models.a: src/gioutils/CMakeFiles/gio-utils.dir/appinfomonitor.cpp.o
src/models/liblauncher-models.a: src/gioutils/CMakeFiles/gio-utils.dir/appinfo.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o
src/models/liblauncher-models.a: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o
src/models/liblauncher-models.a: src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o
src/models/liblauncher-models.a: src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o
src/models/liblauncher-models.a: src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o
src/models/liblauncher-models.a: src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/build.make
src/models/liblauncher-models.a: src/models/CMakeFiles/launcher-models.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Linking CXX static library liblauncher-models.a"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && $(CMAKE_COMMAND) -P CMakeFiles/launcher-models.dir/cmake_clean_target.cmake
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/launcher-models.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/models/CMakeFiles/launcher-models.dir/build: src/models/liblauncher-models.a
.PHONY : src/models/CMakeFiles/launcher-models.dir/build

src/models/CMakeFiles/launcher-models.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models && $(CMAKE_COMMAND) -P CMakeFiles/launcher-models.dir/cmake_clean.cmake
.PHONY : src/models/CMakeFiles/launcher-models.dir/clean

src/models/CMakeFiles/launcher-models.dir/depend: src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp
src/models/CMakeFiles/launcher-models.dir/depend: src/models/launcher-models.qmltypes
src/models/CMakeFiles/launcher-models.dir/depend: src/models/launcher-models_autogen/timestamp
src/models/CMakeFiles/launcher-models.dir/depend: src/models/launcher-models_qmltyperegistrations.cpp
src/models/CMakeFiles/launcher-models.dir/depend: src/models/meta_types/launcher-models_json_file_list.txt
src/models/CMakeFiles/launcher-models.dir/depend: src/models/meta_types/qt6launcher-models_none_metatypes.json
src/models/CMakeFiles/launcher-models.dir/depend: src/models/meta_types/qt6launcher-models_none_metatypes.json.gen
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/models /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/models/CMakeFiles/launcher-models.dir/depend

