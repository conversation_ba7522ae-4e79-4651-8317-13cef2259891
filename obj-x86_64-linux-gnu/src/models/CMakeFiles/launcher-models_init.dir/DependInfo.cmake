
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "" "src/models/launcher-models_init_autogen/timestamp" "custom" "src/models/launcher-models_init_autogen/deps"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_init_autogen/mocs_compilation.cpp" "src/models/CMakeFiles/launcher-models_init.dir/launcher-models_init_autogen/mocs_compilation.cpp.o" "gcc" "src/models/CMakeFiles/launcher-models_init.dir/launcher-models_init_autogen/mocs_compilation.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher_models_init.cpp" "src/models/CMakeFiles/launcher-models_init.dir/launcher_models_init.cpp.o" "gcc" "src/models/CMakeFiles/launcher-models_init.dir/launcher_models_init.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
