# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DDSYSINFO_PREFIX=\"\" -DPREFIX=\"/usr\" -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_DBUS_LIB -DQT_DEPRECATED_WARNINGS -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_PLUGIN -DQT_QMLINTEGRATION_LIB -DQT_QML_LIB -DQT_STATICPLUGIN -DQT_XML_LIB

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/models -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_init_autogen/include -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/include -I/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0 -I/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0/QtQml -I/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0 -I/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore -I/usr/include/x86_64-linux-gnu/qt6/QtQml -I/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration -I/usr/include/x86_64-linux-gnu/qt6/QtNetwork -I/usr/include/x86_64-linux-gnu/qt6/QtGui -I/usr/include/dtk6/DCore -I/usr/include/x86_64-linux-gnu/qt6/QtDBus -I/usr/include/x86_64-linux-gnu/qt6/QtXml -I/usr/include/dtk6/DLog -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/sysprof-6 -I/usr/include/libmount -I/usr/include/blkid -I/usr/include/gio-unix-2.0 -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration -I/home/<USER>/Desktop/myrepo/dde-launchpad/src -I/usr/include/x86_64-linux-gnu/qt6/QtConcurrent -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC -pthread

