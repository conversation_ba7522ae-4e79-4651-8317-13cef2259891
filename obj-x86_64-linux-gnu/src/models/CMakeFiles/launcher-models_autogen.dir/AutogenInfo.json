{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/src/models", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreMacros.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlPluginTemplate.cpp.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlPluginTemplate.cpp.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6QmlModuleDirMappingTemplate.qrc.in", "/usr/lib/x86_64-linux-gnu/cmake/Qt6Qml/Qt6qmldirTemplate.cmake.in", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_org_deepin_launchpad_modelsPlugin_in.cpp"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/deps", "DEP_FILE_RULE_NAME": "launcher-models_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.h", "Mu", "EWIEGA46WW/moc_appitem.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.h", "Mu", "EWIEGA46WW/moc_appsmodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.h", "Mu", "EWIEGA46WW/moc_categorizedsortproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.h", "Mu", "EWIEGA46WW/moc_countlimitproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.h", "Mu", "EWIEGA46WW/moc_favoritedproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.h", "Mu", "EWIEGA46WW/moc_freesortproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.h", "Mu", "EWIEGA46WW/moc_frequentlyusedproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.h", "Mu", "EWIEGA46WW/moc_itemarrangementproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.h", "Mu", "EWIEGA46WW/moc_itemspage.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.h", "Mu", "EWIEGA46WW/moc_itemspagemodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.h", "Mu", "EWIEGA46WW/moc_multipagesortfilterproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.h", "Mu", "EWIEGA46WW/moc_recentlyinstalledproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.h", "Mu", "EWIEGA46WW/moc_searchfilterproxymodel.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.h", "Mu", "EWIEGA46WW/moc_sortproxymodel.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "DSYSINFO_PREFIX=\"\"", "PREFIX=\"/usr\"", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DBUS_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_PLUGIN", "QT_QMLINTEGRATION_LIB", "QT_QML_LIB", "QT_STATICPLUGIN", "QT_XML_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/models", "/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtQml/6.8.0/QtQml", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/gioutils", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration", "/home/<USER>/Desktop/myrepo/dde-launchpad/src", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/include/x86_64-linux-gnu/qt6", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtQml", "/usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration", "/usr/include/x86_64-linux-gnu/qt6/QtNetwork", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/dtk6/DCore", "/usr/include/x86_64-linux-gnu/qt6/QtDBus", "/usr/include/x86_64-linux-gnu/qt6/QtXml", "/usr/include/dtk6/DLog", "/usr/include/glib-2.0", "/usr/lib/x86_64-linux-gnu/glib-2.0/include", "/usr/include/sysprof-6", "/usr/include/libmount", "/usr/include/blkid", "/usr/include/gio-unix-2.0", "/usr/include/x86_64-linux-gnu/qt6/QtConcurrent", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": ["-Muri=org.deepin.launchpad.models", "--output-json"], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/.qt/rcc/qrc_qmake_org_deepin_launchpad_models_init.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_qmltyperegistrations.cpp"], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/CMakeFiles/launcher-models_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/models/launcher-models_org_deepin_launchpad_modelsPlugin.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appitem.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/appsmodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/categorizedsortproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/countlimitproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/favoritedproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/freesortproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/frequentlyusedproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemarrangementproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspage.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/itemspagemodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/multipagesortfilterproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/recentlyinstalledproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/searchfilterproxymodel.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/models/sortproxymodel.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}