/****************************************************************************
** Generated QML type registration code
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <QtQml/qqml.h>
#include <QtQml/qqmlmoduleregistration.h>

#if __has_include(<appitem.h>)
#  include <appitem.h>
#endif
#if __has_include(<appsmodel.h>)
#  include <appsmodel.h>
#endif
#if __has_include(<categorizedsortproxymodel.h>)
#  include <categorizedsortproxymodel.h>
#endif
#if __has_include(<countlimitproxymodel.h>)
#  include <countlimitproxymodel.h>
#endif
#if __has_include(<favoritedproxymodel.h>)
#  include <favoritedproxymodel.h>
#endif
#if __has_include(<freesortproxymodel.h>)
#  include <freesortproxymodel.h>
#endif
#if __has_include(<frequentlyusedproxymodel.h>)
#  include <frequentlyusedproxymodel.h>
#endif
#if __has_include(<itemarrangementproxymodel.h>)
#  include <itemarrangementproxymodel.h>
#endif
#if __has_include(<itemspagemodel.h>)
#  include <itemspagemodel.h>
#endif
#if __has_include(<multipagesortfilterproxymodel.h>)
#  include <multipagesortfilterproxymodel.h>
#endif
#if __has_include(<recentlyinstalledproxymodel.h>)
#  include <recentlyinstalledproxymodel.h>
#endif
#if __has_include(<searchfilterproxymodel.h>)
#  include <searchfilterproxymodel.h>
#endif
#if __has_include(<sortproxymodel.h>)
#  include <sortproxymodel.h>
#endif


#if !defined(QT_STATIC)
#define Q_QMLTYPE_EXPORT Q_DECL_EXPORT
#else
#define Q_QMLTYPE_EXPORT
#endif
Q_QMLTYPE_EXPORT void qml_register_types_org_deepin_launchpad_models()
{
    QT_WARNING_PUSH QT_WARNING_DISABLE_DEPRECATED
    qmlRegisterTypesAndRevisions<AppItem>("org.deepin.launchpad.models", 1);
    QMetaType::fromType<AppItem::Roles>().id();
    QMetaType::fromType<AppItem::DDECategories>().id();
    qmlRegisterTypesAndRevisions<AppsModel>("org.deepin.launchpad.models", 1);
    QMetaType::fromType<AppsModel::Roles>().id();
    qmlRegisterTypesAndRevisions<CategorizedSortProxyModel>("org.deepin.launchpad.models", 1);
    QMetaType::fromType<CategorizedSortProxyModel::CategoryType>().id();
    qmlRegisterTypesAndRevisions<CountLimitProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<FavoritedProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<FreeSortProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<FrequentlyUsedProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<ItemArrangementProxyModel>("org.deepin.launchpad.models", 1);
    QMetaType::fromType<ItemArrangementProxyModel::ItemType>().id();
    QMetaType::fromType<ItemArrangementProxyModel::Roles>().id();
    QMetaType::fromType<ItemArrangementProxyModel::DndOperation>().id();
    qmlRegisterTypesAndRevisions<ItemsPageModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<MultipageSortFilterProxyModel>("org.deepin.launchpad.models", 1);
    QMetaType::fromType<QAbstractItemModel *>().id();
    QMetaType::fromType<QAbstractItemModel::LayoutChangeHint>().id();
    QMetaType::fromType<QAbstractItemModel::CheckIndexOption>().id();
    QMetaType::fromType<QAbstractListModel *>().id();
    QMetaType::fromType<QAbstractProxyModel *>().id();
    QMetaType::fromType<QConcatenateTablesProxyModel *>().id();
    QMetaType::fromType<QSortFilterProxyModel *>().id();
    QMetaType::fromType<QStandardItemModel *>().id();
    qmlRegisterTypesAndRevisions<RecentlyInstalledProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<SearchFilterProxyModel>("org.deepin.launchpad.models", 1);
    qmlRegisterTypesAndRevisions<SortProxyModel>("org.deepin.launchpad.models", 1);
    QT_WARNING_POP
    qmlRegisterModule("org.deepin.launchpad.models", 1, 0);
}

static const QQmlModuleRegistration orgdeepinlaunchpadmodelsRegistration("org.deepin.launchpad.models", qml_register_types_org_deepin_launchpad_models);
