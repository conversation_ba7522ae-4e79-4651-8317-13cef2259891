[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "AppItem"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "AppItem should only be created from C++ side"}], "className": "AppItem", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["DesktopIdRole", "Categories", "DDECategoryRole", "IconNameRole", "NameRole", "InstalledTimeRole", "LastLaunchedTimeRole", "LaunchedTimesRole", "IsAutoStartRole", "VendorRole", "GenericNameRole", "ModelExtendedRole"]}, {"isClass": false, "isFlag": false, "name": "DDECategories", "values": ["Internet", "Cha<PERSON>", "Music", "Video", "Graphics", "Game", "Office", "Reading", "Development", "System", "Others"]}], "gadget": true, "lineNumber": 10, "qualifiedClassName": "AppItem", "superClasses": [{"access": "public", "name": "QStandardItem"}]}], "inputFile": "appitem.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AppsModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "AppsModel", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["TransliteratedRole", "AllTransliteratedRole", "NameRole", "ProxyModelExtendedRole"]}], "lineNumber": 20, "object": true, "qualifiedClassName": "AppsModel", "slots": [{"access": "private", "index": 0, "name": "updateModelData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QStandardItemModel"}]}], "inputFile": "appsmodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CategorizedSortProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "CategorizedSortProxyModel", "enums": [{"isClass": false, "isFlag": false, "name": "CategoryType", "values": ["Alphabetary", "DDECategory", "FreeCategory"]}], "lineNumber": 10, "methods": [{"access": "public", "index": 1, "name": "alphabetarySections", "returnType": "QList<QString>"}, {"access": "public", "index": 2, "name": "DDECategorySections", "returnType": "QList<int>"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "categoryType", "notify": "categoryTypeChanged", "read": "categoryType", "required": false, "scriptable": true, "stored": true, "type": "CategoryType", "user": false, "write": "setCategoryType"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sortRoleName", "notify": "categoryTypeChanged", "read": "sortRoleName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "CategorizedSortProxyModel", "signals": [{"access": "public", "index": 0, "name": "categoryTypeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "categorizedsortproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CountLimitProxyModel"}], "className": "CountLimitProxyModel", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "maxRowCount", "notify": "maxRowCountChanged", "read": "maxRowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxRowCount"}], "qualifiedClassName": "CountLimitProxyModel", "signals": [{"access": "public", "index": 0, "name": "maxRowCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "countlimitproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FavoritedProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "FavoritedProxyModel", "lineNumber": 10, "methods": [{"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 0, "name": "exists", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 1, "name": "addFavorite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 2, "name": "removeFavorite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 3, "name": "pinToTop", "returnType": "void"}], "object": true, "qualifiedClassName": "FavoritedProxyModel", "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "favoritedproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FreeSortProxyModel"}], "className": "FreeSortProxyModel", "lineNumber": 12, "object": true, "qualifiedClassName": "FreeSortProxyModel", "superClasses": [{"access": "public", "name": "SortProxyModel"}]}], "inputFile": "freesortproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "FrequentlyUsedProxyModel"}], "className": "FrequentlyUsedProxyModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "recentlyInstalledModel", "notify": "recentlyInstalledModelChanged", "read": "recentlyInstalledModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setRecentlyInstalledModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_desktopIdRole", "name": "desktopIdRole", "notify": "desktopIdRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_launchedTimesRole", "name": "launchedTimesRole", "notify": "launchedTimesRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_lastLaunchedTimeRole", "name": "lastLaunchedTimeRole", "notify": "lastLaunchedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "FrequentlyUsedProxyModel", "signals": [{"access": "public", "index": 0, "name": "recentlyInstalledModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "desktopIdRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 2, "name": "launchedTimesRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 3, "name": "lastLaunchedTimeRoleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "frequentlyusedproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemArrangementProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "ItemArrangementProxyModel", "enums": [{"isClass": false, "isFlag": false, "name": "ItemType", "values": ["AppItemType", "FolderItemType"]}, {"isClass": false, "isFlag": false, "name": "Roles", "values": ["PageRole", "IndexInPageRole", "FolderIdNumberRole", "IconsNameRole", "ItemTypeRole"]}, {"isClass": false, "isFlag": false, "name": "DndOperation", "values": ["DndPrepend", "DndJoin", "DndAppend"]}], "lineNumber": 14, "methods": [{"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 2, "name": "pageCount", "returnType": "int"}, {"access": "public", "index": 3, "isCloned": true, "name": "pageCount", "returnType": "int"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}, {"name": "name", "type": "QString"}], "index": 4, "name": "updateFolderName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "QString"}], "index": 5, "name": "bringToFront", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dragId", "type": "QString"}, {"name": "dropId", "type": "QString"}, {"name": "op", "type": "DndOperation"}, {"name": "pageHint", "type": "int"}], "index": 6, "name": "commitDndOperation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dragId", "type": "QString"}, {"name": "dropId", "type": "QString"}, {"name": "op", "type": "DndOperation"}], "index": 7, "isCloned": true, "name": "commitDndOperation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 8, "name": "creatEmptyPage", "returnType": "int"}, {"access": "public", "index": 9, "isCloned": true, "name": "creatEmptyPage", "returnType": "int"}, {"access": "public", "index": 10, "name": "removeEmptyPage", "returnType": "void"}], "object": true, "qualifiedClassName": "ItemArrangementProxyModel", "signals": [{"access": "public", "index": 0, "name": "topLevelPageCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "folderId", "type": "int"}], "index": 1, "name": "folderPageCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QConcatenateTablesProxyModel"}]}], "inputFile": "itemarrangementproxymodel.h", "outputRevision": 68}, {"classes": [{"className": "ItemsPage", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "pageCount", "notify": "pageCountChanged", "read": "pageCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "maxItemCountPerPage", "read": "maxItemCountPerPage", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "ItemsPage", "signals": [{"access": "public", "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pageCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 2, "name": "sigPageAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 3, "name": "sigPageRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "itemspage.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ItemsPageModel"}], "className": "ItemsPageModel", "lineNumber": 13, "methods": [{"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}], "index": 1, "name": "rowCount", "returnType": "int"}, {"access": "public", "index": 2, "isCloned": true, "name": "rowCount", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceModel", "notify": "sourceModelChanged", "read": "sourceModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setSourceModel"}], "qualifiedClassName": "ItemsPageModel", "signals": [{"access": "public", "arguments": [{"type": "QObject*"}], "index": 0, "name": "sourceModelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}]}], "inputFile": "itemspagemodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MultipageSortFilterProxyModel"}], "className": "MultipageSortFilterProxyModel", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceModel", "notify": "sourceModelChanged", "read": "sourceModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_folderId", "name": "folderId", "notify": "onFolderIdChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_pageId", "name": "pageId", "notify": "onPageIdChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_filterOnlyMode", "name": "filterOnlyMode", "notify": "onFilterOnlyModeChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "MultipageSortFilterProxyModel", "signals": [{"access": "public", "arguments": [{"type": "int"}], "index": 0, "name": "onFolderIdChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 1, "name": "onPageIdChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QObject*"}], "index": 2, "name": "sourceModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "onFilterOnlyModeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "multipagesortfilterproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RecentlyInstalledProxyModel"}], "className": "RecentlyInstalledProxyModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "member": "m_lastLaunchedTimeRole", "name": "lastLaunchedTimeRole", "notify": "lastLaunchedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_installedTimeRole", "name": "installedTimeRole", "notify": "installedTimeRoleChanged", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "RecentlyInstalledProxyModel", "signals": [{"access": "public", "index": 0, "name": "lastLaunchedTimeRoleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "installedTimeRoleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "recentlyinstalledproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SearchFilterProxyModel"}, {"name": "QML.Singleton", "value": "true"}], "className": "SearchFilterProxyModel", "lineNumber": 14, "object": true, "qualifiedClassName": "SearchFilterProxyModel", "superClasses": [{"access": "public", "name": "QSortFilterProxyModel"}]}], "inputFile": "searchfilterproxymodel.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "SortProxyModel", "lineNumber": 35, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sortRole", "notify": "sortRoleChanged", "read": "sortRole", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSortRole"}], "qualifiedClassName": "SortProxyModel", "signals": [{"access": "public", "index": 0, "name": "sortRoleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "sortCaseSensitivityChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "sortColumnChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "sortOrderChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 4, "name": "resetInternalData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractProxyModel"}]}], "inputFile": "sortproxymodel.h", "outputRevision": 68}, {"classes": [{"className": "org_deepin_launchpad_modelsPlugin", "lineNumber": 13, "object": true, "qualifiedClassName": "org_deepin_launchpad_modelsPlugin", "superClasses": [{"access": "public", "name": "QQmlEngineExtensionPlugin"}]}], "inputFile": "launcher-models_org_deepin_launchpad_modelsPlugin.cpp", "outputRevision": 68}]