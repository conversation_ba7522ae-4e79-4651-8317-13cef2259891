# Generated by CMake. Changes will be overwritten.
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/qrect.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/objectmap.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/amglobaltypes.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QRect
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q23utility.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/dtk6/DCore/dtkcore_config.h
 mdp:/usr/include/dtk6/DCore/dtkcore_global.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMap
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QPointer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_futex.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_ios.h
 mdp:/usr/include/c++/12/bits/basic_ios.tcc
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/codecvt.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/fs_dir.h
 mdp:/usr/include/c++/12/bits/fs_fwd.h
 mdp:/usr/include/c++/12/bits/fs_ops.h
 mdp:/usr/include/c++/12/bits/fs_path.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/istream.tcc
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/locale_conv.h
 mdp:/usr/include/c++/12/bits/locale_facets.h
 mdp:/usr/include/c++/12/bits/locale_facets.tcc
 mdp:/usr/include/c++/12/bits/locale_facets_nonio.h
 mdp:/usr/include/c++/12/bits/locale_facets_nonio.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream.tcc
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/quoted_string.h
 mdp:/usr/include/c++/12/bits/random.h
 mdp:/usr/include/c++/12/bits/random.tcc
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/sstream.tcc
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/std_mutex.h
 mdp:/usr/include/c++/12/bits/std_thread.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_lock.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cassert
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/climits
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/codecvt
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/condition_variable
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/cwctype
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/filesystem
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/future
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iomanip
 mdp:/usr/include/c++/12/ios
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/istream
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/locale
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/mutex
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/ostream
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/random
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/sstream
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/libintl.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/opt_random.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/QtConcurrent
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/QtConcurrentDepends
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtaskbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrent_global.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentcompilertest.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentfilter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentfilterkernel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentfunctionwrappers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentiteratekernel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentmapkernel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentmedian.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentreducekernel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentrun.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentrunbase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentstoredfunctioncall.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrenttask.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentthreadengine.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtConcurrent/qtconcurrentversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QDeadlineTimer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QHash
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QList
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMap
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMutex
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QString
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QStringList
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QVariant
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QtCore
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QtCoreDepends
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20algorithm.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20chrono.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20iterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20map.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20vector.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q23functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q23utility.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q26numeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractanimation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qabstracteventdispatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractitemmodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractnativeeventfilter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractproxymodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanimationgroup.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qapplicationstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassociativeiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomicscopedvaluerollback.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbitarray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbuffer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraymatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcache.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcalendar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborarray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborcommon.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcbormap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamreader.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamwriter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcborvalue.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchronotimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcollator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcommandlineoption.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcommandlineparser.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconcatenatetablesproxymodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication_platform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreevent.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcryptographichash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatetime.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdeadlinetimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdir.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdiriterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdirlisting.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qeasingcurve.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qelapsedtimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qendian.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qeventloop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexception.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfactoryinterface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfile.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfiledevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfileinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfileselector.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfilesystemwatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfuture.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfuture_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfutureinterface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfuturesynchronizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfuturewatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qidentityproxymodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qitemselectionmodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonarray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qjsondocument.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonvalue.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlibrary.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlibraryinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qline.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlockfile.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qloggingcategory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmessageauthenticationcode.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetaobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmimedata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmimedatabase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmimetype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmutex.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnativeinterface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectcleanuphandler.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoperatingsystemversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qparallelanimationgroup.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpauseanimation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpermissions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qplugin.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpluginloader.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocess.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpromise.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qproperty.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyanimation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyprivate.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qqueue.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrandom.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qreadwritelock.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qregularexpression.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qresultstore.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrunnable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsavefile.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedvaluerollback.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsemaphore.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsequentialanimationgroup.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsequentialiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsettings.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedmemory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsignalmapper.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsimd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsocketnotifier.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsortfilterproxymodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qspan.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstack.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstandardpaths.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstaticlatin1stringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstorageinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlistmodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemsemaphore.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtemporarydir.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtemporaryfile.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtextboundaryfinder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qthread.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qthreadpool.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qthreadstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtimeline.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtimezone.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtipccommon.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtranslator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtransposeproxymodel.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtsan_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtsymbolmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtyperevision.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qurl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qurlquery.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/quuid.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantanimation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvarianthash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvector.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversionnumber.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qwaitcondition.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qwineventnotifier.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxmlstream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxpfunctional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/adxintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/amxbf16intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/amxint8intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/amxtileintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx2intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx5124fmapsintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx5124vnniwintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bf16intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bf16vlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bitalgintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bwintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512cdintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512dqintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512erintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fp16intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fp16vlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512ifmaintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512ifmavlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512pfintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmi2intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmi2vlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmiintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmivlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vlbwintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vldqintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vnniintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vnnivlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vp2intersectintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vp2intersectvlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vpopcntdqintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vpopcntdqvlintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avxintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/avxvnniintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/bmi2intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/bmiintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/cetintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/cldemoteintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/clflushoptintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/clwbintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/clzerointrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/emmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/enqcmdintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/f16cintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/fmaintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/fxsrintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/gfniintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/hresetintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/ia32intrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/immintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/keylockerintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/lwpintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/lzcntintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/mm_malloc.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/mmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/movdirintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/mwaitintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/mwaitxintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/pconfigintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/pkuintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/pmmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/popcntintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/prfchwintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/rdseedintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/rtmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/serializeintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/sgxintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/shaintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/smmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/tbmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/tmmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/tsxldtrkintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/uintrintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/vaesintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/vpclmulqdqintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/waitpkgintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/wbnoinvdintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/wmmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/x86gprintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xmmintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xsavecintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xsaveintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xsaveoptintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xsavesintrin.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/xtestintrin.h
