{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6/Dtk6Config.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/Dtk6ToolsConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Core/Dtk6CoreConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Core/Dtk6CoreConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Core/Dtk6CoreTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/Dtk6SettingsToolsMacros.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/Dtk6ToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/Dtk6ToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/DtkDBusMacros.cmake", "/usr/share/cmake-3.31/Modules/MacroAddFileDependencies.cmake", "/usr/share/cmake-3.31/Modules/CMakeParseArguments.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Dtk6Tools/DtkDConfigMacros.cmake", "/usr/share/cmake-3.31/Modules/MacroAddFileDependencies.cmake", "/usr/share/cmake-3.31/Modules/CMakeParseArguments.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/deps", "DEP_FILE_RULE_NAME": "dde-integration-dbus_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.h", "Mu", "EWIEGA46WW/moc_appearance.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.h", "Mu", "EWIEGA46WW/moc_appmgr.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.h", "Mu", "EWIEGA46WW/moc_appwiz.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.h", "Mu", "EWIEGA46WW/moc_ddedock.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/amglobaltypes.h", "Mu", "GZRP3O7STM/moc_amglobaltypes.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/objectmap.h", "Mu", "GZRP3O7STM/moc_objectmap.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/types/qrect.h", "Mu", "GZRP3O7STM/moc_qrect.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "DSYSINFO_PREFIX=\"\"", "PREFIX=\"/usr\"", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DBUS_LIB", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NO_DEBUG", "QT_XML_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration", "/home/<USER>/Desktop/myrepo/dde-launchpad/src", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils", "/usr/include/x86_64-linux-gnu/qt6/QtConcurrent", "/usr/include/x86_64-linux-gnu/qt6", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/dtk6/DCore", "/usr/include/x86_64-linux-gnu/qt6/QtDBus", "/usr/include/x86_64-linux-gnu/qt6/QtXml", "/usr/include/dtk6/DLog", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1Application.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1Application.h", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1ApplicationObjectManager.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1ApplicationObjectManager.h", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/Appearance1.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/Appearance1.h", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonDock1.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonDock1.h", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonLauncher1.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonLauncher1.h", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_AppManager1Application.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_AppManager1ApplicationObjectManager.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_Appearance1.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_DaemonDock1.cpp", "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_DaemonLauncher1.cpp"], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}