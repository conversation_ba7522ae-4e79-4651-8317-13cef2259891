# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.make

# Include the progress variables for this target.
include src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/progress.make

# Include the compile flags for this target's objects.
include src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make

src/ddeintegration/moc_DaemonDock1.cpp: src/ddeintegration/DaemonDock1.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating moc_DaemonDock1.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/lib/qt6/libexec/moc @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_DaemonDock1.cpp_parameters

src/ddeintegration/DaemonDock1.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Dock1.xml
src/ddeintegration/DaemonDock1.cpp: /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating DaemonDock1.cpp, DaemonDock1.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix -N -m -c Dock1 -p DaemonDock1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Dock1.xml

src/ddeintegration/DaemonDock1.h: src/ddeintegration/DaemonDock1.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/ddeintegration/DaemonDock1.h

src/ddeintegration/moc_DaemonLauncher1.cpp: src/ddeintegration/DaemonLauncher1.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Generating moc_DaemonLauncher1.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/lib/qt6/libexec/moc @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_DaemonLauncher1.cpp_parameters

src/ddeintegration/DaemonLauncher1.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Launcher1.xml
src/ddeintegration/DaemonLauncher1.cpp: /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating DaemonLauncher1.cpp, DaemonLauncher1.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix -N -m -c DaemonLauncher1 -p DaemonLauncher1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Launcher1.xml

src/ddeintegration/DaemonLauncher1.h: src/ddeintegration/DaemonLauncher1.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/ddeintegration/DaemonLauncher1.h

src/ddeintegration/moc_Appearance1.cpp: src/ddeintegration/Appearance1.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Generating moc_Appearance1.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/lib/qt6/libexec/moc @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_Appearance1.cpp_parameters

src/ddeintegration/Appearance1.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.Appearance1.xml
src/ddeintegration/Appearance1.cpp: /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Generating Appearance1.cpp, Appearance1.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix -N -m -c Appearance1 -p Appearance1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.Appearance1.xml

src/ddeintegration/Appearance1.h: src/ddeintegration/Appearance1.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/ddeintegration/Appearance1.h

src/ddeintegration/moc_AppManager1Application.cpp: src/ddeintegration/AppManager1Application.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Generating moc_AppManager1Application.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/lib/qt6/libexec/moc @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_AppManager1Application.cpp_parameters

src/ddeintegration/AppManager1Application.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml
src/ddeintegration/AppManager1Application.cpp: /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Generating AppManager1Application.cpp, AppManager1Application.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix -N -m -c AppManager1Application -i types/amglobaltypes.h -p AppManager1Application /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml

src/ddeintegration/AppManager1Application.h: src/ddeintegration/AppManager1Application.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/ddeintegration/AppManager1Application.h

src/ddeintegration/moc_AppManager1ApplicationObjectManager.cpp: src/ddeintegration/AppManager1ApplicationObjectManager.h
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Generating moc_AppManager1ApplicationObjectManager.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/lib/qt6/libexec/moc @/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/moc_AppManager1ApplicationObjectManager.cpp_parameters

src/ddeintegration/AppManager1ApplicationObjectManager.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ObjectManager1.xml
src/ddeintegration/AppManager1ApplicationObjectManager.cpp: /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Generating AppManager1ApplicationObjectManager.cpp, AppManager1ApplicationObjectManager.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/libexec/dtk6/DCore/bin/qdbusxml2cpp-fix -N -m -c AppManager1ApplicationObjectManager -i types/amglobaltypes.h -p AppManager1ApplicationObjectManager /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ObjectManager1.xml

src/ddeintegration/AppManager1ApplicationObjectManager.h: src/ddeintegration/AppManager1ApplicationObjectManager.cpp
	@$(CMAKE_COMMAND) -E touch_nocreate src/ddeintegration/AppManager1ApplicationObjectManager.h

src/ddeintegration/dde-integration-dbus_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/ddeintegration/dde-integration-dbus_autogen/timestamp: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Automatic MOC for target dde-integration-dbus"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/timestamp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/codegen:
.PHONY : src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/codegen

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o: src/ddeintegration/dde-integration-dbus_autogen/mocs_compilation.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/mocs_compilation.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/mocs_compilation.cpp > CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/dde-integration-dbus_autogen/mocs_compilation.cpp -o CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o: src/ddeintegration/DaemonDock1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o: src/ddeintegration/moc_DaemonDock1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonDock1.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonDock1.cpp > CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonDock1.cpp -o CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o: src/ddeintegration/DaemonLauncher1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o: src/ddeintegration/moc_DaemonLauncher1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonLauncher1.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonLauncher1.cpp > CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/DaemonLauncher1.cpp -o CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o: src/ddeintegration/Appearance1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o: src/ddeintegration/moc_Appearance1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/Appearance1.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/Appearance1.cpp > CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/Appearance1.cpp -o CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o: src/ddeintegration/AppManager1Application.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o: src/ddeintegration/moc_AppManager1Application.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1Application.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1Application.cpp > CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1Application.cpp -o CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o: src/ddeintegration/AppManager1ApplicationObjectManager.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o: src/ddeintegration/moc_AppManager1ApplicationObjectManager.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1ApplicationObjectManager.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1ApplicationObjectManager.cpp > CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/AppManager1ApplicationObjectManager.cpp -o CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp > CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appwiz.cpp -o CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp > CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/ddedock.cpp -o CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/appearance.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp > CMakeFiles/dde-integration-dbus.dir/appearance.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/appearance.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appearance.cpp -o CMakeFiles/dde-integration-dbus.dir/appearance.cpp.s

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/flags.make
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o -MF CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o.d -o CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp > CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.i

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/appmgr.cpp -o CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.s

dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/dde-integration-dbus_autogen/mocs_compilation.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonDock1.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DaemonLauncher1.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/Appearance1.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1Application.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/AppManager1ApplicationObjectManager.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appwiz.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/ddedock.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appearance.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/appmgr.cpp.o
dde-integration-dbus: src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/build.make
.PHONY : dde-integration-dbus

# Rule to build all files generated by this target.
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/build: dde-integration-dbus
.PHONY : src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/build

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration && $(CMAKE_COMMAND) -P CMakeFiles/dde-integration-dbus.dir/cmake_clean.cmake
.PHONY : src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/clean

src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/AppManager1Application.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/AppManager1Application.h
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/AppManager1ApplicationObjectManager.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/AppManager1ApplicationObjectManager.h
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/Appearance1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/Appearance1.h
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/DaemonDock1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/DaemonDock1.h
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/DaemonLauncher1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/DaemonLauncher1.h
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/dde-integration-dbus_autogen/timestamp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/moc_AppManager1Application.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/moc_AppManager1ApplicationObjectManager.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/moc_Appearance1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/moc_DaemonDock1.cpp
src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend: src/ddeintegration/moc_DaemonLauncher1.cpp
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/ddeintegration/CMakeFiles/dde-integration-dbus.dir/depend

