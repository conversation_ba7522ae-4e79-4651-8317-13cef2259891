/****************************************************************************
** Meta object code from reading C++ file 'DaemonDock1.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "DaemonDock1.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DaemonDock1.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASS__Dock1ENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASS__Dock1ENDCLASS = QtMocHelpers::stringData(
    "__Dock1",
    "DockAppSettingsSynced",
    "",
    "EntryAdded",
    "QDBusObjectPath",
    "in0",
    "in1",
    "EntryRemoved",
    "PluginSettingsSynced",
    "ServiceRestarted",
    "DisplayModeChanged",
    "value",
    "DockedAppsChanged",
    "EntriesChanged",
    "QList<QDBusObjectPath>",
    "FrontendWindowRectChanged",
    "HideModeChanged",
    "HideStateChanged",
    "HideTimeoutChanged",
    "IconSizeChanged",
    "OpacityChanged",
    "PositionChanged",
    "ShowTimeoutChanged",
    "WindowMarginChanged",
    "WindowSizeChanged",
    "WindowSizeEfficientChanged",
    "WindowSizeFashionChanged",
    "ActivateWindow",
    "QDBusPendingReply<>",
    "ActivateWindowQueued",
    "CancelPreviewWindow",
    "CancelPreviewWindowQueued",
    "CloseWindow",
    "CloseWindowQueued",
    "GetDockedAppsDesktopFiles",
    "QDBusPendingReply<QStringList>",
    "GetEntryIDs",
    "GetPluginSettings",
    "QDBusPendingReply<QString>",
    "IsDocked",
    "QDBusPendingReply<bool>",
    "IsOnDock",
    "MakeWindowAbove",
    "MakeWindowAboveQueued",
    "MaximizeWindow",
    "MaximizeWindowQueued",
    "MergePluginSettings",
    "MergePluginSettingsQueued",
    "MinimizeWindow",
    "MinimizeWindowQueued",
    "MoveEntry",
    "MoveEntryQueued",
    "MoveWindow",
    "MoveWindowQueued",
    "PreviewWindow",
    "PreviewWindowQueued",
    "QueryWindowIdentifyMethod",
    "RemovePluginSettings",
    "RemovePluginSettingsQueued",
    "RequestDock",
    "RequestUndock",
    "SetFrontendWindowRect",
    "in2",
    "in3",
    "SetFrontendWindowRectQueued",
    "SetPluginSettings",
    "SetPluginSettingsQueued",
    "CallQueued",
    "callName",
    "QList<QVariant>",
    "args",
    "onPendingCallFinished",
    "QDBusPendingCallWatcher*",
    "w",
    "onPropertyChanged",
    "propName",
    "QVariant",
    "DisplayMode",
    "DockedApps",
    "Entries",
    "FrontendWindowRect",
    "HideMode",
    "HideState",
    "HideTimeout",
    "IconSize",
    "Opacity",
    "Position",
    "ShowTimeout",
    "WindowMargin",
    "WindowSize",
    "WindowSizeEfficient",
    "WindowSizeFashion"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASS__Dock1ENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      57,   14, // methods
      15,  539, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      20,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  356,    2, 0x06,   16 /* Public */,
       3,    2,  357,    2, 0x06,   17 /* Public */,
       7,    1,  362,    2, 0x06,   20 /* Public */,
       8,    0,  365,    2, 0x06,   22 /* Public */,
       9,    0,  366,    2, 0x06,   23 /* Public */,
      10,    1,  367,    2, 0x106,   24 /* Public | MethodIsConst  */,
      12,    1,  370,    2, 0x106,   26 /* Public | MethodIsConst  */,
      13,    1,  373,    2, 0x106,   28 /* Public | MethodIsConst  */,
      15,    1,  376,    2, 0x106,   30 /* Public | MethodIsConst  */,
      16,    1,  379,    2, 0x106,   32 /* Public | MethodIsConst  */,
      17,    1,  382,    2, 0x106,   34 /* Public | MethodIsConst  */,
      18,    1,  385,    2, 0x106,   36 /* Public | MethodIsConst  */,
      19,    1,  388,    2, 0x106,   38 /* Public | MethodIsConst  */,
      20,    1,  391,    2, 0x106,   40 /* Public | MethodIsConst  */,
      21,    1,  394,    2, 0x106,   42 /* Public | MethodIsConst  */,
      22,    1,  397,    2, 0x106,   44 /* Public | MethodIsConst  */,
      23,    1,  400,    2, 0x106,   46 /* Public | MethodIsConst  */,
      24,    1,  403,    2, 0x106,   48 /* Public | MethodIsConst  */,
      25,    1,  406,    2, 0x106,   50 /* Public | MethodIsConst  */,
      26,    1,  409,    2, 0x106,   52 /* Public | MethodIsConst  */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      27,    1,  412,    2, 0x0a,   54 /* Public */,
      29,    1,  415,    2, 0x0a,   56 /* Public */,
      30,    0,  418,    2, 0x0a,   58 /* Public */,
      31,    0,  419,    2, 0x0a,   59 /* Public */,
      32,    1,  420,    2, 0x0a,   60 /* Public */,
      33,    1,  423,    2, 0x0a,   62 /* Public */,
      34,    0,  426,    2, 0x0a,   64 /* Public */,
      36,    0,  427,    2, 0x0a,   65 /* Public */,
      37,    0,  428,    2, 0x0a,   66 /* Public */,
      39,    1,  429,    2, 0x0a,   67 /* Public */,
      41,    1,  432,    2, 0x0a,   69 /* Public */,
      42,    1,  435,    2, 0x0a,   71 /* Public */,
      43,    1,  438,    2, 0x0a,   73 /* Public */,
      44,    1,  441,    2, 0x0a,   75 /* Public */,
      45,    1,  444,    2, 0x0a,   77 /* Public */,
      46,    1,  447,    2, 0x0a,   79 /* Public */,
      47,    1,  450,    2, 0x0a,   81 /* Public */,
      48,    1,  453,    2, 0x0a,   83 /* Public */,
      49,    1,  456,    2, 0x0a,   85 /* Public */,
      50,    2,  459,    2, 0x0a,   87 /* Public */,
      51,    2,  464,    2, 0x0a,   90 /* Public */,
      52,    1,  469,    2, 0x0a,   93 /* Public */,
      53,    1,  472,    2, 0x0a,   95 /* Public */,
      54,    1,  475,    2, 0x0a,   97 /* Public */,
      55,    1,  478,    2, 0x0a,   99 /* Public */,
      56,    1,  481,    2, 0x0a,  101 /* Public */,
      57,    2,  484,    2, 0x0a,  103 /* Public */,
      58,    2,  489,    2, 0x0a,  106 /* Public */,
      59,    2,  494,    2, 0x0a,  109 /* Public */,
      60,    1,  499,    2, 0x0a,  112 /* Public */,
      61,    4,  502,    2, 0x0a,  114 /* Public */,
      64,    4,  511,    2, 0x0a,  119 /* Public */,
      65,    1,  520,    2, 0x0a,  124 /* Public */,
      66,    1,  523,    2, 0x0a,  126 /* Public */,
      67,    2,  526,    2, 0x0a,  128 /* Public */,
      71,    1,  531,    2, 0x08,  131 /* Private */,
      74,    2,  534,    2, 0x08,  133 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 4, QMetaType::Int,    5,    6,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::QStringList,   11,
    QMetaType::Void, 0x80000000 | 14,   11,
    QMetaType::Void, QMetaType::QRect,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::Double,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::UInt,   11,
    QMetaType::Void, QMetaType::UInt,   11,

 // slots: parameters
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 28,
    QMetaType::Void,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 35,
    0x80000000 | 35,
    0x80000000 | 38,
    0x80000000 | 40, QMetaType::QString,    5,
    0x80000000 | 40, QMetaType::QString,    5,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 28, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 28, QMetaType::Int, QMetaType::Int,    5,    6,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    5,    6,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 28, QMetaType::UInt,    5,
    QMetaType::Void, QMetaType::UInt,    5,
    0x80000000 | 38, QMetaType::UInt,    5,
    0x80000000 | 28, QMetaType::QString, QMetaType::QStringList,    5,    6,
    QMetaType::Void, QMetaType::QString, QMetaType::QStringList,    5,    6,
    0x80000000 | 40, QMetaType::QString, QMetaType::Int,    5,    6,
    0x80000000 | 40, QMetaType::QString,    5,
    0x80000000 | 28, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,    5,    6,   62,   63,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,    5,    6,   62,   63,
    0x80000000 | 28, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString,    5,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 69,   68,   70,
    QMetaType::Void, 0x80000000 | 72,   73,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 76,   75,   11,

 // properties: name, type, flags, notifyId, revision
      77, QMetaType::Int, 0x00015103, uint(5), 0,
      78, QMetaType::QStringList, 0x00015001, uint(6), 0,
      79, 0x80000000 | 14, 0x00015009, uint(7), 0,
      80, QMetaType::QRect, 0x00015001, uint(8), 0,
      81, QMetaType::Int, 0x00015103, uint(9), 0,
      82, QMetaType::Int, 0x00015001, uint(10), 0,
      83, QMetaType::UInt, 0x00015103, uint(11), 0,
      84, QMetaType::UInt, 0x00015103, uint(12), 0,
      85, QMetaType::Double, 0x00015103, uint(13), 0,
      86, QMetaType::Int, 0x00015103, uint(14), 0,
      87, QMetaType::UInt, 0x00015103, uint(15), 0,
      88, QMetaType::UInt, 0x00015001, uint(16), 0,
      89, QMetaType::UInt, 0x00015103, uint(17), 0,
      90, QMetaType::UInt, 0x00015103, uint(18), 0,
      91, QMetaType::UInt, 0x00015103, uint(19), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject __Dock1::staticMetaObject = { {
    QMetaObject::SuperData::link<DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::staticMetaObject>(),
    qt_meta_stringdata_CLASS__Dock1ENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASS__Dock1ENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASS__Dock1ENDCLASS_t,
        // property 'DisplayMode'
        QtPrivate::TypeAndForceComplete<int, std::true_type>,
        // property 'DockedApps'
        QtPrivate::TypeAndForceComplete<QStringList, std::true_type>,
        // property 'Entries'
        QtPrivate::TypeAndForceComplete<QList<QDBusObjectPath>, std::true_type>,
        // property 'FrontendWindowRect'
        QtPrivate::TypeAndForceComplete<QRect, std::true_type>,
        // property 'HideMode'
        QtPrivate::TypeAndForceComplete<int, std::true_type>,
        // property 'HideState'
        QtPrivate::TypeAndForceComplete<int, std::true_type>,
        // property 'HideTimeout'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'IconSize'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'Opacity'
        QtPrivate::TypeAndForceComplete<double, std::true_type>,
        // property 'Position'
        QtPrivate::TypeAndForceComplete<int, std::true_type>,
        // property 'ShowTimeout'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'WindowMargin'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'WindowSize'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'WindowSizeEfficient'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // property 'WindowSizeFashion'
        QtPrivate::TypeAndForceComplete<uint, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<__Dock1, std::true_type>,
        // method 'DockAppSettingsSynced'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'EntryAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QDBusObjectPath &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'EntryRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'PluginSettingsSynced'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'ServiceRestarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'DisplayModeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'DockedAppsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'EntriesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QDBusObjectPath> &, std::false_type>,
        // method 'FrontendWindowRectChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QRect &, std::false_type>,
        // method 'HideModeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'HideStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'HideTimeoutChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'IconSizeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'OpacityChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'PositionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'ShowTimeoutChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'WindowMarginChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'WindowSizeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'WindowSizeEfficientChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'WindowSizeFashionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'ActivateWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'ActivateWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'CancelPreviewWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        // method 'CancelPreviewWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'CloseWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'CloseWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'GetDockedAppsDesktopFiles'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QStringList>, std::false_type>,
        // method 'GetEntryIDs'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QStringList>, std::false_type>,
        // method 'GetPluginSettings'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        // method 'IsDocked'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'IsOnDock'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'MakeWindowAbove'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MakeWindowAboveQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MaximizeWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MaximizeWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MergePluginSettings'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'MergePluginSettingsQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'MinimizeWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MinimizeWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MoveEntry'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'MoveEntryQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'MoveWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'MoveWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'PreviewWindow'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'PreviewWindowQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'QueryWindowIdentifyMethod'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        QtPrivate::TypeAndForceComplete<uint, std::false_type>,
        // method 'RemovePluginSettings'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'RemovePluginSettingsQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'RequestDock'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'RequestUndock'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'SetFrontendWindowRect'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'SetFrontendWindowRectQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'SetPluginSettings'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'SetPluginSettingsQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'CallQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QVariant> &, std::false_type>,
        // method 'onPendingCallFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDBusPendingCallWatcher *, std::false_type>,
        // method 'onPropertyChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariant &, std::false_type>
    >,
    nullptr
} };

void __Dock1::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<__Dock1 *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->DockAppSettingsSynced(); break;
        case 1: _t->EntryAdded((*reinterpret_cast< std::add_pointer_t<QDBusObjectPath>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 2: _t->EntryRemoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->PluginSettingsSynced(); break;
        case 4: _t->ServiceRestarted(); break;
        case 5: _t->DisplayModeChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 6: _t->DockedAppsChanged((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 7: _t->EntriesChanged((*reinterpret_cast< std::add_pointer_t<QList<QDBusObjectPath>>>(_a[1]))); break;
        case 8: _t->FrontendWindowRectChanged((*reinterpret_cast< std::add_pointer_t<QRect>>(_a[1]))); break;
        case 9: _t->HideModeChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 10: _t->HideStateChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 11: _t->HideTimeoutChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 12: _t->IconSizeChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 13: _t->OpacityChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 14: _t->PositionChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 15: _t->ShowTimeoutChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 16: _t->WindowMarginChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 17: _t->WindowSizeChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 18: _t->WindowSizeEfficientChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 19: _t->WindowSizeFashionChanged((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 20: { QDBusPendingReply<> _r = _t->ActivateWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 21: _t->ActivateWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 22: { QDBusPendingReply<> _r = _t->CancelPreviewWindow();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 23: _t->CancelPreviewWindowQueued(); break;
        case 24: { QDBusPendingReply<> _r = _t->CloseWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 25: _t->CloseWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 26: { QDBusPendingReply<QStringList> _r = _t->GetDockedAppsDesktopFiles();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QStringList>*>(_a[0]) = std::move(_r); }  break;
        case 27: { QDBusPendingReply<QStringList> _r = _t->GetEntryIDs();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QStringList>*>(_a[0]) = std::move(_r); }  break;
        case 28: { QDBusPendingReply<QString> _r = _t->GetPluginSettings();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 29: { QDBusPendingReply<bool> _r = _t->IsDocked((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 30: { QDBusPendingReply<bool> _r = _t->IsOnDock((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 31: { QDBusPendingReply<> _r = _t->MakeWindowAbove((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 32: _t->MakeWindowAboveQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 33: { QDBusPendingReply<> _r = _t->MaximizeWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 34: _t->MaximizeWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 35: { QDBusPendingReply<> _r = _t->MergePluginSettings((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 36: _t->MergePluginSettingsQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 37: { QDBusPendingReply<> _r = _t->MinimizeWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 38: _t->MinimizeWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 39: { QDBusPendingReply<> _r = _t->MoveEntry((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 40: _t->MoveEntryQueued((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 41: { QDBusPendingReply<> _r = _t->MoveWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 42: _t->MoveWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 43: { QDBusPendingReply<> _r = _t->PreviewWindow((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 44: _t->PreviewWindowQueued((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1]))); break;
        case 45: { QDBusPendingReply<QString> _r = _t->QueryWindowIdentifyMethod((*reinterpret_cast< std::add_pointer_t<uint>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 46: { QDBusPendingReply<> _r = _t->RemovePluginSettings((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 47: _t->RemovePluginSettingsQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2]))); break;
        case 48: { QDBusPendingReply<bool> _r = _t->RequestDock((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 49: { QDBusPendingReply<bool> _r = _t->RequestUndock((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 50: { QDBusPendingReply<> _r = _t->SetFrontendWindowRect((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 51: _t->SetFrontendWindowRectQueued((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 52: { QDBusPendingReply<> _r = _t->SetPluginSettings((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 53: _t->SetPluginSettingsQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 54: _t->CallQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QList<QVariant>>>(_a[2]))); break;
        case 55: _t->onPendingCallFinished((*reinterpret_cast< std::add_pointer_t<QDBusPendingCallWatcher*>>(_a[1]))); break;
        case 56: _t->onPropertyChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (__Dock1::*)();
            if (_t _q_method = &__Dock1::DockAppSettingsSynced; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(const QDBusObjectPath & , int );
            if (_t _q_method = &__Dock1::EntryAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(const QString & );
            if (_t _q_method = &__Dock1::EntryRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)();
            if (_t _q_method = &__Dock1::PluginSettingsSynced; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)();
            if (_t _q_method = &__Dock1::ServiceRestarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(int ) const;
            if (_t _q_method = &__Dock1::DisplayModeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(const QStringList & ) const;
            if (_t _q_method = &__Dock1::DockedAppsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(const QList<QDBusObjectPath> & ) const;
            if (_t _q_method = &__Dock1::EntriesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(const QRect & ) const;
            if (_t _q_method = &__Dock1::FrontendWindowRectChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(int ) const;
            if (_t _q_method = &__Dock1::HideModeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(int ) const;
            if (_t _q_method = &__Dock1::HideStateChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::HideTimeoutChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::IconSizeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(double ) const;
            if (_t _q_method = &__Dock1::OpacityChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 13;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(int ) const;
            if (_t _q_method = &__Dock1::PositionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 14;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::ShowTimeoutChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 15;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::WindowMarginChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 16;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::WindowSizeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 17;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::WindowSizeEfficientChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 18;
                return;
            }
        }
        {
            using _t = void (__Dock1::*)(uint ) const;
            if (_t _q_method = &__Dock1::WindowSizeFashionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 19;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<__Dock1 *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< int*>(_v) = _t->displayMode(); break;
        case 1: *reinterpret_cast< QStringList*>(_v) = _t->dockedApps(); break;
        case 2: *reinterpret_cast< QList<QDBusObjectPath>*>(_v) = _t->entries(); break;
        case 3: *reinterpret_cast< QRect*>(_v) = _t->frontendWindowRect(); break;
        case 4: *reinterpret_cast< int*>(_v) = _t->hideMode(); break;
        case 5: *reinterpret_cast< int*>(_v) = _t->hideState(); break;
        case 6: *reinterpret_cast< uint*>(_v) = _t->hideTimeout(); break;
        case 7: *reinterpret_cast< uint*>(_v) = _t->iconSize(); break;
        case 8: *reinterpret_cast< double*>(_v) = _t->opacity(); break;
        case 9: *reinterpret_cast< int*>(_v) = _t->position(); break;
        case 10: *reinterpret_cast< uint*>(_v) = _t->showTimeout(); break;
        case 11: *reinterpret_cast< uint*>(_v) = _t->windowMargin(); break;
        case 12: *reinterpret_cast< uint*>(_v) = _t->windowSize(); break;
        case 13: *reinterpret_cast< uint*>(_v) = _t->windowSizeEfficient(); break;
        case 14: *reinterpret_cast< uint*>(_v) = _t->windowSizeFashion(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<__Dock1 *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setDisplayMode(*reinterpret_cast< int*>(_v)); break;
        case 4: _t->setHideMode(*reinterpret_cast< int*>(_v)); break;
        case 6: _t->setHideTimeout(*reinterpret_cast< uint*>(_v)); break;
        case 7: _t->setIconSize(*reinterpret_cast< uint*>(_v)); break;
        case 8: _t->setOpacity(*reinterpret_cast< double*>(_v)); break;
        case 9: _t->setPosition(*reinterpret_cast< int*>(_v)); break;
        case 10: _t->setShowTimeout(*reinterpret_cast< uint*>(_v)); break;
        case 12: _t->setWindowSize(*reinterpret_cast< uint*>(_v)); break;
        case 13: _t->setWindowSizeEfficient(*reinterpret_cast< uint*>(_v)); break;
        case 14: _t->setWindowSizeFashion(*reinterpret_cast< uint*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *__Dock1::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *__Dock1::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASS__Dock1ENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacast(_clname);
}

int __Dock1::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 57)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 57;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 57)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 57;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void __Dock1::DockAppSettingsSynced()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void __Dock1::EntryAdded(const QDBusObjectPath & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void __Dock1::EntryRemoved(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void __Dock1::PluginSettingsSynced()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void __Dock1::ServiceRestarted()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void __Dock1::DisplayModeChanged(int _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 5, _a);
}

// SIGNAL 6
void __Dock1::DockedAppsChanged(const QStringList & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 6, _a);
}

// SIGNAL 7
void __Dock1::EntriesChanged(const QList<QDBusObjectPath> & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 7, _a);
}

// SIGNAL 8
void __Dock1::FrontendWindowRectChanged(const QRect & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 8, _a);
}

// SIGNAL 9
void __Dock1::HideModeChanged(int _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 9, _a);
}

// SIGNAL 10
void __Dock1::HideStateChanged(int _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 10, _a);
}

// SIGNAL 11
void __Dock1::HideTimeoutChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 11, _a);
}

// SIGNAL 12
void __Dock1::IconSizeChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 12, _a);
}

// SIGNAL 13
void __Dock1::OpacityChanged(double _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 13, _a);
}

// SIGNAL 14
void __Dock1::PositionChanged(int _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 14, _a);
}

// SIGNAL 15
void __Dock1::ShowTimeoutChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 15, _a);
}

// SIGNAL 16
void __Dock1::WindowMarginChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 16, _a);
}

// SIGNAL 17
void __Dock1::WindowSizeChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 17, _a);
}

// SIGNAL 18
void __Dock1::WindowSizeEfficientChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 18, _a);
}

// SIGNAL 19
void __Dock1::WindowSizeFashionChanged(uint _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Dock1 *>(this), &staticMetaObject, 19, _a);
}
QT_WARNING_POP
