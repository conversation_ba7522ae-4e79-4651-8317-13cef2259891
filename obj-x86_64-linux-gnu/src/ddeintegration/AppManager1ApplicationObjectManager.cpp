/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c AppManager1ApplicationObjectManager -i types/amglobaltypes.h -p AppManager1ApplicationObjectManager /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ObjectManager1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "AppManager1ApplicationObjectManager.h"

DCORE_USE_NAMESPACE
/*
 * Implementation of interface class __AppManager1ApplicationObjectManager
 */

class __AppManager1ApplicationObjectManagerPrivate
{
public:
   __AppManager1ApplicationObjectManagerPrivate() = default;

    // begin member variables

public:
    QMap<QString, QDBusPendingCallWatcher *> m_processingCalls;
    QMap<QString, QList<QVariant>> m_waittingCalls;
};

__AppManager1ApplicationObjectManager::__AppManager1ApplicationObjectManager(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent)
    : DDBusExtendedAbstractInterface(service, path, staticInterfaceName(), connection, parent)
    , d_ptr(new __AppManager1ApplicationObjectManagerPrivate)
{
    if (QMetaType::fromName("ObjectMap").id() == QMetaType::UnknownType)
        registerObjectMapMetaType();
}

__AppManager1ApplicationObjectManager::~__AppManager1ApplicationObjectManager()
{
    qDeleteAll(d_ptr->m_processingCalls.values());
    delete d_ptr;
}

void __AppManager1ApplicationObjectManager::CallQueued(const QString &callName, const QList<QVariant> &args)
{
    if (d_ptr->m_waittingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls[callName] = args;
        return;
    }
    if (d_ptr->m_processingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls.insert(callName, args);
    } else {
        QDBusPendingCallWatcher *watcher = new QDBusPendingCallWatcher(asyncCallWithArgumentList(callName, args));
        connect(watcher, &QDBusPendingCallWatcher::finished, this, &__AppManager1ApplicationObjectManager::onPendingCallFinished);
        d_ptr->m_processingCalls.insert(callName, watcher);
    }
}

void __AppManager1ApplicationObjectManager::onPendingCallFinished(QDBusPendingCallWatcher *w)
{
    w->deleteLater();
    const auto callName = d_ptr->m_processingCalls.key(w);
    Q_ASSERT(!callName.isEmpty());
    if (callName.isEmpty())
        return;
    d_ptr->m_processingCalls.remove(callName);
    if (!d_ptr->m_waittingCalls.contains(callName))
        return;
    const auto args = d_ptr->m_waittingCalls.take(callName);
    CallQueued(callName, args);
}

#include "moc_AppManager1ApplicationObjectManager.cpp"
