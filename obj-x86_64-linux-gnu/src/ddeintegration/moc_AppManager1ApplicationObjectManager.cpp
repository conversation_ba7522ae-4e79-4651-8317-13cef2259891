/****************************************************************************
** Meta object code from reading C++ file 'AppManager1ApplicationObjectManager.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "AppManager1ApplicationObjectManager.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AppManager1ApplicationObjectManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASS__AppManager1ApplicationObjectManagerENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASS__AppManager1ApplicationObjectManagerENDCLASS = QtMocHelpers::stringData(
    "__AppManager1ApplicationObjectManager",
    "InterfacesAdded",
    "",
    "QDBusObjectPath",
    "object_path",
    "ObjectInterfaceMap",
    "interfaces_and_properties",
    "InterfacesRemoved",
    "interfaces",
    "GetManagedObjects",
    "QDBusPendingReply<ObjectMap>",
    "CallQueued",
    "callName",
    "QList<QVariant>",
    "args",
    "onPendingCallFinished",
    "QDBusPendingCallWatcher*",
    "w"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASS__AppManager1ApplicationObjectManagerENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    2,   44,    2, 0x06,    1 /* Public */,
       7,    2,   49,    2, 0x06,    4 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       9,    0,   54,    2, 0x0a,    7 /* Public */,
      11,    2,   55,    2, 0x0a,    8 /* Public */,
      15,    1,   60,    2, 0x08,   11 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5,    4,    6,
    QMetaType::Void, 0x80000000 | 3, QMetaType::QStringList,    4,    8,

 // slots: parameters
    0x80000000 | 10,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 13,   12,   14,
    QMetaType::Void, 0x80000000 | 16,   17,

       0        // eod
};

Q_CONSTINIT const QMetaObject __AppManager1ApplicationObjectManager::staticMetaObject = { {
    QMetaObject::SuperData::link<DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::staticMetaObject>(),
    qt_meta_stringdata_CLASS__AppManager1ApplicationObjectManagerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASS__AppManager1ApplicationObjectManagerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASS__AppManager1ApplicationObjectManagerENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<__AppManager1ApplicationObjectManager, std::true_type>,
        // method 'InterfacesAdded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QDBusObjectPath &, std::false_type>,
        QtPrivate::TypeAndForceComplete<ObjectInterfaceMap, std::false_type>,
        // method 'InterfacesRemoved'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QDBusObjectPath &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'GetManagedObjects'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<ObjectMap>, std::false_type>,
        // method 'CallQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QVariant> &, std::false_type>,
        // method 'onPendingCallFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDBusPendingCallWatcher *, std::false_type>
    >,
    nullptr
} };

void __AppManager1ApplicationObjectManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<__AppManager1ApplicationObjectManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->InterfacesAdded((*reinterpret_cast< std::add_pointer_t<QDBusObjectPath>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ObjectInterfaceMap>>(_a[2]))); break;
        case 1: _t->InterfacesRemoved((*reinterpret_cast< std::add_pointer_t<QDBusObjectPath>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2]))); break;
        case 2: { QDBusPendingReply<ObjectMap> _r = _t->GetManagedObjects();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<ObjectMap>*>(_a[0]) = std::move(_r); }  break;
        case 3: _t->CallQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QList<QVariant>>>(_a[2]))); break;
        case 4: _t->onPendingCallFinished((*reinterpret_cast< std::add_pointer_t<QDBusPendingCallWatcher*>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 1:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< ObjectInterfaceMap >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (__AppManager1ApplicationObjectManager::*)(const QDBusObjectPath & , ObjectInterfaceMap );
            if (_t _q_method = &__AppManager1ApplicationObjectManager::InterfacesAdded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (__AppManager1ApplicationObjectManager::*)(const QDBusObjectPath & , const QStringList & );
            if (_t _q_method = &__AppManager1ApplicationObjectManager::InterfacesRemoved; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
    }
}

const QMetaObject *__AppManager1ApplicationObjectManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *__AppManager1ApplicationObjectManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASS__AppManager1ApplicationObjectManagerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacast(_clname);
}

int __AppManager1ApplicationObjectManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void __AppManager1ApplicationObjectManager::InterfacesAdded(const QDBusObjectPath & _t1, ObjectInterfaceMap _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void __AppManager1ApplicationObjectManager::InterfacesRemoved(const QDBusObjectPath & _t1, const QStringList & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}
QT_WARNING_POP
