/****************************************************************************
** Meta object code from reading C++ file 'AppManager1Application.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "AppManager1Application.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AppManager1Application.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASS__AppManager1ApplicationENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASS__AppManager1ApplicationENDCLASS = QtMocHelpers::stringData(
    "__AppManager1Application",
    "ActionNameChanged",
    "",
    "PropMap",
    "value",
    "ActionsChanged",
    "AutoStartChanged",
    "CategoriesChanged",
    "EnvironChanged",
    "GenericNameChanged",
    "QStringMap",
    "IDChanged",
    "IconsChanged",
    "InstalledTimeChanged",
    "InstancesChanged",
    "QList<QDBusObjectPath>",
    "LastLaunchedTimeChanged",
    "LaunchedTimesChanged",
    "MimeTypesChanged",
    "NameChanged",
    "NoDisplayChanged",
    "TerminalChanged",
    "X_Deepin_VendorChanged",
    "X_FlatpakChanged",
    "X_linglongChanged",
    "IsOnDesktopChanged",
    "Launch",
    "QDBusPendingReply<QDBusObjectPath>",
    "action",
    "fields",
    "QVariantMap",
    "options",
    "RemoveFromDesktop",
    "QDBusPendingReply<bool>",
    "SendToDesktop",
    "CallQueued",
    "callName",
    "QList<QVariant>",
    "args",
    "onPendingCallFinished",
    "QDBusPendingCallWatcher*",
    "w",
    "onPropertyChanged",
    "propName",
    "QVariant",
    "ActionName",
    "Actions",
    "AutoStart",
    "Categories",
    "Environ",
    "GenericName",
    "ID",
    "Icons",
    "InstalledTime",
    "Instances",
    "LastLaunchedTime",
    "LaunchedTimes",
    "MimeTypes",
    "Name",
    "NoDisplay",
    "Terminal",
    "X_Deepin_Vendor",
    "X_Flatpak",
    "X_linglong",
    "isOnDesktop"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASS__AppManager1ApplicationENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      26,   14, // methods
      20,  252, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      20,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  170,    2, 0x106,   21 /* Public | MethodIsConst  */,
       5,    1,  173,    2, 0x106,   23 /* Public | MethodIsConst  */,
       6,    1,  176,    2, 0x106,   25 /* Public | MethodIsConst  */,
       7,    1,  179,    2, 0x106,   27 /* Public | MethodIsConst  */,
       8,    1,  182,    2, 0x106,   29 /* Public | MethodIsConst  */,
       9,    1,  185,    2, 0x106,   31 /* Public | MethodIsConst  */,
      11,    1,  188,    2, 0x106,   33 /* Public | MethodIsConst  */,
      12,    1,  191,    2, 0x106,   35 /* Public | MethodIsConst  */,
      13,    1,  194,    2, 0x106,   37 /* Public | MethodIsConst  */,
      14,    1,  197,    2, 0x106,   39 /* Public | MethodIsConst  */,
      16,    1,  200,    2, 0x106,   41 /* Public | MethodIsConst  */,
      17,    1,  203,    2, 0x106,   43 /* Public | MethodIsConst  */,
      18,    1,  206,    2, 0x106,   45 /* Public | MethodIsConst  */,
      19,    1,  209,    2, 0x106,   47 /* Public | MethodIsConst  */,
      20,    1,  212,    2, 0x106,   49 /* Public | MethodIsConst  */,
      21,    1,  215,    2, 0x106,   51 /* Public | MethodIsConst  */,
      22,    1,  218,    2, 0x106,   53 /* Public | MethodIsConst  */,
      23,    1,  221,    2, 0x106,   55 /* Public | MethodIsConst  */,
      24,    1,  224,    2, 0x106,   57 /* Public | MethodIsConst  */,
      25,    1,  227,    2, 0x106,   59 /* Public | MethodIsConst  */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      26,    3,  230,    2, 0x0a,   61 /* Public */,
      32,    0,  237,    2, 0x0a,   65 /* Public */,
      34,    0,  238,    2, 0x0a,   66 /* Public */,
      35,    2,  239,    2, 0x0a,   67 /* Public */,
      39,    1,  244,    2, 0x08,   70 /* Private */,
      42,    2,  247,    2, 0x08,   72 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QStringList,    4,
    QMetaType::Void, QMetaType::Bool,    4,
    QMetaType::Void, QMetaType::QStringList,    4,
    QMetaType::Void, QMetaType::QString,    4,
    QMetaType::Void, 0x80000000 | 10,    4,
    QMetaType::Void, QMetaType::QString,    4,
    QMetaType::Void, 0x80000000 | 10,    4,
    QMetaType::Void, QMetaType::LongLong,    4,
    QMetaType::Void, 0x80000000 | 15,    4,
    QMetaType::Void, QMetaType::LongLong,    4,
    QMetaType::Void, QMetaType::LongLong,    4,
    QMetaType::Void, QMetaType::QStringList,    4,
    QMetaType::Void, 0x80000000 | 10,    4,
    QMetaType::Void, QMetaType::Bool,    4,
    QMetaType::Void, QMetaType::Bool,    4,
    QMetaType::Void, QMetaType::QString,    4,
    QMetaType::Void, QMetaType::Bool,    4,
    QMetaType::Void, QMetaType::Bool,    4,
    QMetaType::Void, QMetaType::Bool,    4,

 // slots: parameters
    0x80000000 | 27, QMetaType::QString, QMetaType::QStringList, 0x80000000 | 30,   28,   29,   31,
    0x80000000 | 33,
    0x80000000 | 33,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 37,   36,   38,
    QMetaType::Void, 0x80000000 | 40,   41,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 44,   43,    4,

 // properties: name, type, flags, notifyId, revision
      45, 0x80000000 | 3, 0x00015009, uint(0), 0,
      46, QMetaType::QStringList, 0x00015001, uint(1), 0,
      47, QMetaType::Bool, 0x00015103, uint(2), 0,
      48, QMetaType::QStringList, 0x00015001, uint(3), 0,
      49, QMetaType::QString, 0x00015103, uint(4), 0,
      50, 0x80000000 | 10, 0x00015009, uint(5), 0,
      51, QMetaType::QString, 0x00015001, uint(6), 0,
      52, 0x80000000 | 10, 0x00015009, uint(7), 0,
      53, QMetaType::LongLong, 0x00015001, uint(8), 0,
      54, 0x80000000 | 15, 0x00015009, uint(9), 0,
      55, QMetaType::LongLong, 0x00015001, uint(10), 0,
      56, QMetaType::LongLong, 0x00015001, uint(11), 0,
      57, QMetaType::QStringList, 0x00015103, uint(12), 0,
      58, 0x80000000 | 10, 0x00015009, uint(13), 0,
      59, QMetaType::Bool, 0x00015001, uint(14), 0,
      60, QMetaType::Bool, 0x00015001, uint(15), 0,
      61, QMetaType::QString, 0x00015001, uint(16), 0,
      62, QMetaType::Bool, 0x00015001, uint(17), 0,
      63, QMetaType::Bool, 0x00015001, uint(18), 0,
      64, QMetaType::Bool, 0x00015001, uint(19), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject __AppManager1Application::staticMetaObject = { {
    QMetaObject::SuperData::link<DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::staticMetaObject>(),
    qt_meta_stringdata_CLASS__AppManager1ApplicationENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASS__AppManager1ApplicationENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASS__AppManager1ApplicationENDCLASS_t,
        // property 'ActionName'
        QtPrivate::TypeAndForceComplete<PropMap, std::true_type>,
        // property 'Actions'
        QtPrivate::TypeAndForceComplete<QStringList, std::true_type>,
        // property 'AutoStart'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // property 'Categories'
        QtPrivate::TypeAndForceComplete<QStringList, std::true_type>,
        // property 'Environ'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'GenericName'
        QtPrivate::TypeAndForceComplete<QStringMap, std::true_type>,
        // property 'ID'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'Icons'
        QtPrivate::TypeAndForceComplete<QStringMap, std::true_type>,
        // property 'InstalledTime'
        QtPrivate::TypeAndForceComplete<qlonglong, std::true_type>,
        // property 'Instances'
        QtPrivate::TypeAndForceComplete<QList<QDBusObjectPath>, std::true_type>,
        // property 'LastLaunchedTime'
        QtPrivate::TypeAndForceComplete<qlonglong, std::true_type>,
        // property 'LaunchedTimes'
        QtPrivate::TypeAndForceComplete<qlonglong, std::true_type>,
        // property 'MimeTypes'
        QtPrivate::TypeAndForceComplete<QStringList, std::true_type>,
        // property 'Name'
        QtPrivate::TypeAndForceComplete<QStringMap, std::true_type>,
        // property 'NoDisplay'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // property 'Terminal'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // property 'X_Deepin_Vendor'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'X_Flatpak'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // property 'X_linglong'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // property 'isOnDesktop'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<__AppManager1Application, std::true_type>,
        // method 'ActionNameChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<PropMap, std::false_type>,
        // method 'ActionsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'AutoStartChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'CategoriesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'EnvironChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'GenericNameChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringMap &, std::false_type>,
        // method 'IDChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'IconsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringMap &, std::false_type>,
        // method 'InstalledTimeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qlonglong, std::false_type>,
        // method 'InstancesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QDBusObjectPath> &, std::false_type>,
        // method 'LastLaunchedTimeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qlonglong, std::false_type>,
        // method 'LaunchedTimesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qlonglong, std::false_type>,
        // method 'MimeTypesChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'NameChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringMap &, std::false_type>,
        // method 'NoDisplayChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'TerminalChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'X_Deepin_VendorChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'X_FlatpakChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'X_linglongChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'IsOnDesktopChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'Launch'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QDBusObjectPath>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariantMap &, std::false_type>,
        // method 'RemoveFromDesktop'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        // method 'SendToDesktop'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<bool>, std::false_type>,
        // method 'CallQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QVariant> &, std::false_type>,
        // method 'onPendingCallFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDBusPendingCallWatcher *, std::false_type>,
        // method 'onPropertyChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariant &, std::false_type>
    >,
    nullptr
} };

void __AppManager1Application::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<__AppManager1Application *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->ActionNameChanged((*reinterpret_cast< std::add_pointer_t<PropMap>>(_a[1]))); break;
        case 1: _t->ActionsChanged((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 2: _t->AutoStartChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 3: _t->CategoriesChanged((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 4: _t->EnvironChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->GenericNameChanged((*reinterpret_cast< std::add_pointer_t<QStringMap>>(_a[1]))); break;
        case 6: _t->IDChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->IconsChanged((*reinterpret_cast< std::add_pointer_t<QStringMap>>(_a[1]))); break;
        case 8: _t->InstalledTimeChanged((*reinterpret_cast< std::add_pointer_t<qlonglong>>(_a[1]))); break;
        case 9: _t->InstancesChanged((*reinterpret_cast< std::add_pointer_t<QList<QDBusObjectPath>>>(_a[1]))); break;
        case 10: _t->LastLaunchedTimeChanged((*reinterpret_cast< std::add_pointer_t<qlonglong>>(_a[1]))); break;
        case 11: _t->LaunchedTimesChanged((*reinterpret_cast< std::add_pointer_t<qlonglong>>(_a[1]))); break;
        case 12: _t->MimeTypesChanged((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 13: _t->NameChanged((*reinterpret_cast< std::add_pointer_t<QStringMap>>(_a[1]))); break;
        case 14: _t->NoDisplayChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 15: _t->TerminalChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 16: _t->X_Deepin_VendorChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: _t->X_FlatpakChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 18: _t->X_linglongChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 19: _t->IsOnDesktopChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 20: { QDBusPendingReply<QDBusObjectPath> _r = _t->Launch((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[3])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QDBusObjectPath>*>(_a[0]) = std::move(_r); }  break;
        case 21: { QDBusPendingReply<bool> _r = _t->RemoveFromDesktop();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 22: { QDBusPendingReply<bool> _r = _t->SendToDesktop();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<bool>*>(_a[0]) = std::move(_r); }  break;
        case 23: _t->CallQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QList<QVariant>>>(_a[2]))); break;
        case 24: _t->onPendingCallFinished((*reinterpret_cast< std::add_pointer_t<QDBusPendingCallWatcher*>>(_a[1]))); break;
        case 25: _t->onPropertyChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< PropMap >(); break;
            }
            break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QStringMap >(); break;
            }
            break;
        case 7:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QStringMap >(); break;
            }
            break;
        case 13:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QStringMap >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (__AppManager1Application::*)(PropMap ) const;
            if (_t _q_method = &__AppManager1Application::ActionNameChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringList & ) const;
            if (_t _q_method = &__AppManager1Application::ActionsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::AutoStartChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringList & ) const;
            if (_t _q_method = &__AppManager1Application::CategoriesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QString & ) const;
            if (_t _q_method = &__AppManager1Application::EnvironChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringMap & ) const;
            if (_t _q_method = &__AppManager1Application::GenericNameChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QString & ) const;
            if (_t _q_method = &__AppManager1Application::IDChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringMap & ) const;
            if (_t _q_method = &__AppManager1Application::IconsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(qlonglong ) const;
            if (_t _q_method = &__AppManager1Application::InstalledTimeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QList<QDBusObjectPath> & ) const;
            if (_t _q_method = &__AppManager1Application::InstancesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(qlonglong ) const;
            if (_t _q_method = &__AppManager1Application::LastLaunchedTimeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(qlonglong ) const;
            if (_t _q_method = &__AppManager1Application::LaunchedTimesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringList & ) const;
            if (_t _q_method = &__AppManager1Application::MimeTypesChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QStringMap & ) const;
            if (_t _q_method = &__AppManager1Application::NameChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 13;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::NoDisplayChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 14;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::TerminalChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 15;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(const QString & ) const;
            if (_t _q_method = &__AppManager1Application::X_Deepin_VendorChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 16;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::X_FlatpakChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 17;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::X_linglongChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 18;
                return;
            }
        }
        {
            using _t = void (__AppManager1Application::*)(bool ) const;
            if (_t _q_method = &__AppManager1Application::IsOnDesktopChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 19;
                return;
            }
        }
    } else if (_c == QMetaObject::RegisterPropertyMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< PropMap >(); break;
        case 13:
        case 7:
        case 5:
            *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QStringMap >(); break;
        }
    }  else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<__AppManager1Application *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< PropMap*>(_v) = _t->actionName(); break;
        case 1: *reinterpret_cast< QStringList*>(_v) = _t->actions(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->autoStart(); break;
        case 3: *reinterpret_cast< QStringList*>(_v) = _t->categories(); break;
        case 4: *reinterpret_cast< QString*>(_v) = _t->environ(); break;
        case 5: *reinterpret_cast< QStringMap*>(_v) = _t->genericName(); break;
        case 6: *reinterpret_cast< QString*>(_v) = _t->iD(); break;
        case 7: *reinterpret_cast< QStringMap*>(_v) = _t->icons(); break;
        case 8: *reinterpret_cast< qlonglong*>(_v) = _t->installedTime(); break;
        case 9: *reinterpret_cast< QList<QDBusObjectPath>*>(_v) = _t->instances(); break;
        case 10: *reinterpret_cast< qlonglong*>(_v) = _t->lastLaunchedTime(); break;
        case 11: *reinterpret_cast< qlonglong*>(_v) = _t->launchedTimes(); break;
        case 12: *reinterpret_cast< QStringList*>(_v) = _t->mimeTypes(); break;
        case 13: *reinterpret_cast< QStringMap*>(_v) = _t->name(); break;
        case 14: *reinterpret_cast< bool*>(_v) = _t->noDisplay(); break;
        case 15: *reinterpret_cast< bool*>(_v) = _t->terminal(); break;
        case 16: *reinterpret_cast< QString*>(_v) = _t->x_Deepin_Vendor(); break;
        case 17: *reinterpret_cast< bool*>(_v) = _t->x_Flatpak(); break;
        case 18: *reinterpret_cast< bool*>(_v) = _t->x_linglong(); break;
        case 19: *reinterpret_cast< bool*>(_v) = _t->isOnDesktop(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<__AppManager1Application *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setAutoStart(*reinterpret_cast< bool*>(_v)); break;
        case 4: _t->setEnviron(*reinterpret_cast< QString*>(_v)); break;
        case 12: _t->setMimeTypes(*reinterpret_cast< QStringList*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *__AppManager1Application::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *__AppManager1Application::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASS__AppManager1ApplicationENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacast(_clname);
}

int __AppManager1Application::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 26)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 26;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 26)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 26;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    }
    return _id;
}

// SIGNAL 0
void __AppManager1Application::ActionNameChanged(PropMap _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 0, _a);
}

// SIGNAL 1
void __AppManager1Application::ActionsChanged(const QStringList & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 1, _a);
}

// SIGNAL 2
void __AppManager1Application::AutoStartChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 2, _a);
}

// SIGNAL 3
void __AppManager1Application::CategoriesChanged(const QStringList & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 3, _a);
}

// SIGNAL 4
void __AppManager1Application::EnvironChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 4, _a);
}

// SIGNAL 5
void __AppManager1Application::GenericNameChanged(const QStringMap & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 5, _a);
}

// SIGNAL 6
void __AppManager1Application::IDChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 6, _a);
}

// SIGNAL 7
void __AppManager1Application::IconsChanged(const QStringMap & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 7, _a);
}

// SIGNAL 8
void __AppManager1Application::InstalledTimeChanged(qlonglong _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 8, _a);
}

// SIGNAL 9
void __AppManager1Application::InstancesChanged(const QList<QDBusObjectPath> & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 9, _a);
}

// SIGNAL 10
void __AppManager1Application::LastLaunchedTimeChanged(qlonglong _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 10, _a);
}

// SIGNAL 11
void __AppManager1Application::LaunchedTimesChanged(qlonglong _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 11, _a);
}

// SIGNAL 12
void __AppManager1Application::MimeTypesChanged(const QStringList & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 12, _a);
}

// SIGNAL 13
void __AppManager1Application::NameChanged(const QStringMap & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 13, _a);
}

// SIGNAL 14
void __AppManager1Application::NoDisplayChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 14, _a);
}

// SIGNAL 15
void __AppManager1Application::TerminalChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 15, _a);
}

// SIGNAL 16
void __AppManager1Application::X_Deepin_VendorChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 16, _a);
}

// SIGNAL 17
void __AppManager1Application::X_FlatpakChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 17, _a);
}

// SIGNAL 18
void __AppManager1Application::X_linglongChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 18, _a);
}

// SIGNAL 19
void __AppManager1Application::IsOnDesktopChanged(bool _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __AppManager1Application *>(this), &staticMetaObject, 19, _a);
}
QT_WARNING_POP
