/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c DaemonLauncher1 -p DaemonLauncher1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Launcher1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef DAEMONLAUNCHER1_H
#define DAEMONLAUNCHER1_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

#include <DDBusExtendedAbstractInterface>
#include <QtDBus/QtDBus>


/*
 * Proxy class for interface org.deepin.dde.daemon.Launcher1
 */
class __DaemonLauncher1Private;
class __DaemonLauncher1 : public DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface
{
    Q_OBJECT

public:
    static inline const char *staticInterfaceName()
    { return "org.deepin.dde.daemon.Launcher1"; }

public:
    explicit __DaemonLauncher1(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = 0);

    ~__DaemonLauncher1();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<> RequestUninstall(const QString &desktop, bool skipPreinstallHook)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(desktop) << QVariant::fromValue(skipPreinstallHook);
        return asyncCallWithArgumentList(QStringLiteral("RequestUninstall"), argumentList);
    }

    inline void RequestUninstallQueued(const QString &desktop, bool skipPreinstallHook)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(desktop) << QVariant::fromValue(skipPreinstallHook);

        CallQueued(QStringLiteral("RequestUninstall"), argumentList);
    }



Q_SIGNALS: // SIGNALS
    void UninstallFailed(const QString &appId, const QString &errMsg);
    void UninstallSuccess(const QString &appID);
    // begin property changed signals

public Q_SLOTS:
    void CallQueued(const QString &callName, const QList<QVariant> &args);

private Q_SLOTS:
    void onPendingCallFinished(QDBusPendingCallWatcher *w);

private:
    __DaemonLauncher1Private *d_ptr;
};

#endif
