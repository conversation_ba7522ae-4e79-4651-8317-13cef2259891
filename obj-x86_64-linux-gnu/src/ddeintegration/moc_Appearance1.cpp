/****************************************************************************
** Meta object code from reading C++ file 'Appearance1.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.8.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "Appearance1.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'Appearance1.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.8.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASS__Appearance1ENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASS__Appearance1ENDCLASS = QtMocHelpers::stringData(
    "__Appearance1",
    "Changed",
    "",
    "in0",
    "in1",
    "Refreshed",
    "BackgroundChanged",
    "value",
    "CursorThemeChanged",
    "FontSizeChanged",
    "GlobalThemeChanged",
    "GtkThemeChanged",
    "IconThemeChanged",
    "MonospaceFontChanged",
    "OpacityChanged",
    "QtActiveColorChanged",
    "StandardFontChanged",
    "WallpaperSlideShowChanged",
    "WallpaperURlsChanged",
    "Delete",
    "QDBusPendingReply<>",
    "DeleteQueued",
    "GetCurrentWorkspaceBackgroundForMonitor",
    "QDBusPendingReply<QString>",
    "strMonitorName",
    "GetScaleFactor",
    "QDBusPendingReply<double>",
    "GetScreenScaleFactors",
    "QDBusPendingReply<QMap<QString,double>>",
    "List",
    "Set",
    "SetQueued",
    "SetScaleFactor",
    "SetScaleFactorQueued",
    "SetScreenScaleFactors",
    "QMap<QString,double>",
    "scaleFactors",
    "SetScreenScaleFactorsQueued",
    "Show",
    "Thumbnail",
    "CallQueued",
    "callName",
    "QList<QVariant>",
    "args",
    "onPendingCallFinished",
    "QDBusPendingCallWatcher*",
    "w",
    "onPropertyChanged",
    "propName",
    "QVariant",
    "Background",
    "CursorTheme",
    "FontSize",
    "GlobalTheme",
    "GtkTheme",
    "IconTheme",
    "MonospaceFont",
    "Opacity",
    "QtActiveColor",
    "StandardFont",
    "WallpaperSlideShow",
    "WallpaperURls"
);
#else  // !QT_MOC_HAS_STRINGDATA
#error "qtmochelpers.h not found or too old."
#endif // !QT_MOC_HAS_STRINGDATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASS__Appearance1ENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
      31,   14, // methods
      12,  307, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      14,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    2,  200,    2, 0x06,   13 /* Public */,
       5,    1,  205,    2, 0x06,   16 /* Public */,
       6,    1,  208,    2, 0x106,   18 /* Public | MethodIsConst  */,
       8,    1,  211,    2, 0x106,   20 /* Public | MethodIsConst  */,
       9,    1,  214,    2, 0x106,   22 /* Public | MethodIsConst  */,
      10,    1,  217,    2, 0x106,   24 /* Public | MethodIsConst  */,
      11,    1,  220,    2, 0x106,   26 /* Public | MethodIsConst  */,
      12,    1,  223,    2, 0x106,   28 /* Public | MethodIsConst  */,
      13,    1,  226,    2, 0x106,   30 /* Public | MethodIsConst  */,
      14,    1,  229,    2, 0x106,   32 /* Public | MethodIsConst  */,
      15,    1,  232,    2, 0x106,   34 /* Public | MethodIsConst  */,
      16,    1,  235,    2, 0x106,   36 /* Public | MethodIsConst  */,
      17,    1,  238,    2, 0x106,   38 /* Public | MethodIsConst  */,
      18,    1,  241,    2, 0x106,   40 /* Public | MethodIsConst  */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      19,    2,  244,    2, 0x0a,   42 /* Public */,
      21,    2,  249,    2, 0x0a,   45 /* Public */,
      22,    1,  254,    2, 0x0a,   48 /* Public */,
      25,    0,  257,    2, 0x0a,   50 /* Public */,
      27,    0,  258,    2, 0x0a,   51 /* Public */,
      29,    1,  259,    2, 0x0a,   52 /* Public */,
      30,    2,  262,    2, 0x0a,   54 /* Public */,
      31,    2,  267,    2, 0x0a,   57 /* Public */,
      32,    1,  272,    2, 0x0a,   60 /* Public */,
      33,    1,  275,    2, 0x0a,   62 /* Public */,
      34,    1,  278,    2, 0x0a,   64 /* Public */,
      37,    1,  281,    2, 0x0a,   66 /* Public */,
      38,    2,  284,    2, 0x0a,   68 /* Public */,
      39,    2,  289,    2, 0x0a,   71 /* Public */,
      40,    2,  294,    2, 0x0a,   74 /* Public */,
      44,    1,  299,    2, 0x08,   77 /* Private */,
      47,    2,  302,    2, 0x08,   79 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Double,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::Double,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,

 // slots: parameters
    0x80000000 | 20, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    0x80000000 | 23, QMetaType::QString,   24,
    0x80000000 | 26,
    0x80000000 | 28,
    0x80000000 | 23, QMetaType::QString,    3,
    0x80000000 | 20, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    0x80000000 | 20, QMetaType::Double,    3,
    QMetaType::Void, QMetaType::Double,    3,
    0x80000000 | 20, 0x80000000 | 35,   36,
    QMetaType::Void, 0x80000000 | 35,   36,
    0x80000000 | 23, QMetaType::QString, QMetaType::QStringList,    3,    4,
    0x80000000 | 23, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 42,   41,   43,
    QMetaType::Void, 0x80000000 | 45,   46,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 49,   48,    7,

 // properties: name, type, flags, notifyId, revision
      50, QMetaType::QString, 0x00015103, uint(2), 0,
      51, QMetaType::QString, 0x00015103, uint(3), 0,
      52, QMetaType::Double, 0x00015103, uint(4), 0,
      53, QMetaType::QString, 0x00015001, uint(5), 0,
      54, QMetaType::QString, 0x00015103, uint(6), 0,
      55, QMetaType::QString, 0x00015103, uint(7), 0,
      56, QMetaType::QString, 0x00015103, uint(8), 0,
      57, QMetaType::Double, 0x00015103, uint(9), 0,
      58, QMetaType::QString, 0x00015103, uint(10), 0,
      59, QMetaType::QString, 0x00015103, uint(11), 0,
      60, QMetaType::QString, 0x00015103, uint(12), 0,
      61, QMetaType::QString, 0x00015001, uint(13), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject __Appearance1::staticMetaObject = { {
    QMetaObject::SuperData::link<DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::staticMetaObject>(),
    qt_meta_stringdata_CLASS__Appearance1ENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASS__Appearance1ENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASS__Appearance1ENDCLASS_t,
        // property 'Background'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'CursorTheme'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'FontSize'
        QtPrivate::TypeAndForceComplete<double, std::true_type>,
        // property 'GlobalTheme'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'GtkTheme'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'IconTheme'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'MonospaceFont'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'Opacity'
        QtPrivate::TypeAndForceComplete<double, std::true_type>,
        // property 'QtActiveColor'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'StandardFont'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'WallpaperSlideShow'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'WallpaperURls'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<__Appearance1, std::true_type>,
        // method 'Changed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'Refreshed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'BackgroundChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'CursorThemeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'FontSizeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'GlobalThemeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'GtkThemeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'IconThemeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'MonospaceFontChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'OpacityChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'QtActiveColorChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'StandardFontChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'WallpaperSlideShowChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'WallpaperURlsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'Delete'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'DeleteQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'GetCurrentWorkspaceBackgroundForMonitor'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'GetScaleFactor'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<double>, std::false_type>,
        // method 'GetScreenScaleFactors'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QMap<QString,double> >, std::false_type>,
        // method 'List'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'Set'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'SetQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'SetScaleFactor'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'SetScaleFactorQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'SetScreenScaleFactors'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QMap<QString,double> &, std::false_type>,
        // method 'SetScreenScaleFactorsQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QMap<QString,double> &, std::false_type>,
        // method 'Show'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QStringList &, std::false_type>,
        // method 'Thumbnail'
        QtPrivate::TypeAndForceComplete<QDBusPendingReply<QString>, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'CallQueued'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QList<QVariant> &, std::false_type>,
        // method 'onPendingCallFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QDBusPendingCallWatcher *, std::false_type>,
        // method 'onPropertyChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariant &, std::false_type>
    >,
    nullptr
} };

void __Appearance1::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<__Appearance1 *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->Changed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 1: _t->Refreshed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 2: _t->BackgroundChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->CursorThemeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->FontSizeChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 5: _t->GlobalThemeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->GtkThemeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->IconThemeChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->MonospaceFontChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: _t->OpacityChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 10: _t->QtActiveColorChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->StandardFontChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 12: _t->WallpaperSlideShowChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->WallpaperURlsChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: { QDBusPendingReply<> _r = _t->Delete((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 15: _t->DeleteQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 16: { QDBusPendingReply<QString> _r = _t->GetCurrentWorkspaceBackgroundForMonitor((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 17: { QDBusPendingReply<double> _r = _t->GetScaleFactor();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<double>*>(_a[0]) = std::move(_r); }  break;
        case 18: { QDBusPendingReply<QMap<QString,double>> _r = _t->GetScreenScaleFactors();
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QMap<QString,double>>*>(_a[0]) = std::move(_r); }  break;
        case 19: { QDBusPendingReply<QString> _r = _t->List((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 20: { QDBusPendingReply<> _r = _t->Set((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 21: _t->SetQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 22: { QDBusPendingReply<> _r = _t->SetScaleFactor((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 23: _t->SetScaleFactorQueued((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 24: { QDBusPendingReply<> _r = _t->SetScreenScaleFactors((*reinterpret_cast< std::add_pointer_t<QMap<QString,double>>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<>*>(_a[0]) = std::move(_r); }  break;
        case 25: _t->SetScreenScaleFactorsQueued((*reinterpret_cast< std::add_pointer_t<QMap<QString,double>>>(_a[1]))); break;
        case 26: { QDBusPendingReply<QString> _r = _t->Show((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 27: { QDBusPendingReply<QString> _r = _t->Thumbnail((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QDBusPendingReply<QString>*>(_a[0]) = std::move(_r); }  break;
        case 28: _t->CallQueued((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QList<QVariant>>>(_a[2]))); break;
        case 29: _t->onPendingCallFinished((*reinterpret_cast< std::add_pointer_t<QDBusPendingCallWatcher*>>(_a[1]))); break;
        case 30: _t->onPropertyChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (__Appearance1::*)(const QString & , const QString & );
            if (_t _q_method = &__Appearance1::Changed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & );
            if (_t _q_method = &__Appearance1::Refreshed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::BackgroundChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::CursorThemeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(double ) const;
            if (_t _q_method = &__Appearance1::FontSizeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::GlobalThemeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::GtkThemeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::IconThemeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::MonospaceFontChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(double ) const;
            if (_t _q_method = &__Appearance1::OpacityChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::QtActiveColorChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::StandardFontChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::WallpaperSlideShowChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (__Appearance1::*)(const QString & ) const;
            if (_t _q_method = &__Appearance1::WallpaperURlsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 13;
                return;
            }
        }
    } else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<__Appearance1 *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->background(); break;
        case 1: *reinterpret_cast< QString*>(_v) = _t->cursorTheme(); break;
        case 2: *reinterpret_cast< double*>(_v) = _t->fontSize(); break;
        case 3: *reinterpret_cast< QString*>(_v) = _t->globalTheme(); break;
        case 4: *reinterpret_cast< QString*>(_v) = _t->gtkTheme(); break;
        case 5: *reinterpret_cast< QString*>(_v) = _t->iconTheme(); break;
        case 6: *reinterpret_cast< QString*>(_v) = _t->monospaceFont(); break;
        case 7: *reinterpret_cast< double*>(_v) = _t->opacity(); break;
        case 8: *reinterpret_cast< QString*>(_v) = _t->qtActiveColor(); break;
        case 9: *reinterpret_cast< QString*>(_v) = _t->standardFont(); break;
        case 10: *reinterpret_cast< QString*>(_v) = _t->wallpaperSlideShow(); break;
        case 11: *reinterpret_cast< QString*>(_v) = _t->wallpaperURls(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<__Appearance1 *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBackground(*reinterpret_cast< QString*>(_v)); break;
        case 1: _t->setCursorTheme(*reinterpret_cast< QString*>(_v)); break;
        case 2: _t->setFontSize(*reinterpret_cast< double*>(_v)); break;
        case 4: _t->setGtkTheme(*reinterpret_cast< QString*>(_v)); break;
        case 5: _t->setIconTheme(*reinterpret_cast< QString*>(_v)); break;
        case 6: _t->setMonospaceFont(*reinterpret_cast< QString*>(_v)); break;
        case 7: _t->setOpacity(*reinterpret_cast< double*>(_v)); break;
        case 8: _t->setQtActiveColor(*reinterpret_cast< QString*>(_v)); break;
        case 9: _t->setStandardFont(*reinterpret_cast< QString*>(_v)); break;
        case 10: _t->setWallpaperSlideShow(*reinterpret_cast< QString*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *__Appearance1::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *__Appearance1::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASS__Appearance1ENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacast(_clname);
}

int __Appearance1::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 31)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 31;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 31)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 31;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void __Appearance1::Changed(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void __Appearance1::Refreshed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void __Appearance1::BackgroundChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 2, _a);
}

// SIGNAL 3
void __Appearance1::CursorThemeChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 3, _a);
}

// SIGNAL 4
void __Appearance1::FontSizeChanged(double _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 4, _a);
}

// SIGNAL 5
void __Appearance1::GlobalThemeChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 5, _a);
}

// SIGNAL 6
void __Appearance1::GtkThemeChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 6, _a);
}

// SIGNAL 7
void __Appearance1::IconThemeChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 7, _a);
}

// SIGNAL 8
void __Appearance1::MonospaceFontChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 8, _a);
}

// SIGNAL 9
void __Appearance1::OpacityChanged(double _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 9, _a);
}

// SIGNAL 10
void __Appearance1::QtActiveColorChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 10, _a);
}

// SIGNAL 11
void __Appearance1::StandardFontChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 11, _a);
}

// SIGNAL 12
void __Appearance1::WallpaperSlideShowChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 12, _a);
}

// SIGNAL 13
void __Appearance1::WallpaperURlsChanged(const QString & _t1)const
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(const_cast< __Appearance1 *>(this), &staticMetaObject, 13, _a);
}
QT_WARNING_POP
