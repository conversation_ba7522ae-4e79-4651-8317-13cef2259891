/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c Dock1 -p DaemonDock1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.daemon.Dock1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "DaemonDock1.h"

DCORE_USE_NAMESPACE
/*
 * Implementation of interface class __Dock1
 */

class __Dock1Private
{
public:
   __Dock1Private() = default;

    // begin member variables
    int DisplayMode;
    QStringList DockedApps;
    QList<QDBusObjectPath> Entries;
    QRect FrontendWindowRect;
    int HideMode;
    int HideState;
    uint HideTimeout;
    uint IconSize;
    double Opacity;
    int Position;
    uint ShowTimeout;
    uint WindowMargin;
    uint WindowSize;
    uint WindowSizeEfficient;
    uint WindowSizeFashion;

public:
    QMap<QString, QDBusPendingCallWatcher *> m_processingCalls;
    QMap<QString, QList<QVariant>> m_waittingCalls;
};

__Dock1::__Dock1(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent)
    : DDBusExtendedAbstractInterface(service, path, staticInterfaceName(), connection, parent)
    , d_ptr(new __Dock1Private)
{
    connect(this, &__Dock1::propertyChanged, this, &__Dock1::onPropertyChanged);

    if (QMetaType::fromName("QRect").id() == QMetaType::UnknownType)
        registerQRectMetaType();
}

__Dock1::~__Dock1()
{
    qDeleteAll(d_ptr->m_processingCalls.values());
    delete d_ptr;
}

void __Dock1::onPropertyChanged(const QString &propName, const QVariant &value)
{
    if (propName == QStringLiteral("DisplayMode"))
    {
        const int &DisplayMode = qvariant_cast<int>(value);
        if (d_ptr->DisplayMode != DisplayMode)
        {
            d_ptr->DisplayMode = DisplayMode;
            Q_EMIT DisplayModeChanged(d_ptr->DisplayMode);
        }
        return;
    }

    if (propName == QStringLiteral("DockedApps"))
    {
        const QStringList &DockedApps = qvariant_cast<QStringList>(value);
        if (d_ptr->DockedApps != DockedApps)
        {
            d_ptr->DockedApps = DockedApps;
            Q_EMIT DockedAppsChanged(d_ptr->DockedApps);
        }
        return;
    }

    if (propName == QStringLiteral("Entries"))
    {
        const QList<QDBusObjectPath> &Entries = qvariant_cast<QList<QDBusObjectPath>>(value);
        if (d_ptr->Entries != Entries)
        {
            d_ptr->Entries = Entries;
            Q_EMIT EntriesChanged(d_ptr->Entries);
        }
        return;
    }

    if (propName == QStringLiteral("FrontendWindowRect"))
    {
        const QRect &FrontendWindowRect = qvariant_cast<QRect>(value);
        if (d_ptr->FrontendWindowRect != FrontendWindowRect)
        {
            d_ptr->FrontendWindowRect = FrontendWindowRect;
            Q_EMIT FrontendWindowRectChanged(d_ptr->FrontendWindowRect);
        }
        return;
    }

    if (propName == QStringLiteral("HideMode"))
    {
        const int &HideMode = qvariant_cast<int>(value);
        if (d_ptr->HideMode != HideMode)
        {
            d_ptr->HideMode = HideMode;
            Q_EMIT HideModeChanged(d_ptr->HideMode);
        }
        return;
    }

    if (propName == QStringLiteral("HideState"))
    {
        const int &HideState = qvariant_cast<int>(value);
        if (d_ptr->HideState != HideState)
        {
            d_ptr->HideState = HideState;
            Q_EMIT HideStateChanged(d_ptr->HideState);
        }
        return;
    }

    if (propName == QStringLiteral("HideTimeout"))
    {
        const uint &HideTimeout = qvariant_cast<uint>(value);
        if (d_ptr->HideTimeout != HideTimeout)
        {
            d_ptr->HideTimeout = HideTimeout;
            Q_EMIT HideTimeoutChanged(d_ptr->HideTimeout);
        }
        return;
    }

    if (propName == QStringLiteral("IconSize"))
    {
        const uint &IconSize = qvariant_cast<uint>(value);
        if (d_ptr->IconSize != IconSize)
        {
            d_ptr->IconSize = IconSize;
            Q_EMIT IconSizeChanged(d_ptr->IconSize);
        }
        return;
    }

    if (propName == QStringLiteral("Opacity"))
    {
        const double &Opacity = qvariant_cast<double>(value);
        if (d_ptr->Opacity != Opacity)
        {
            d_ptr->Opacity = Opacity;
            Q_EMIT OpacityChanged(d_ptr->Opacity);
        }
        return;
    }

    if (propName == QStringLiteral("Position"))
    {
        const int &Position = qvariant_cast<int>(value);
        if (d_ptr->Position != Position)
        {
            d_ptr->Position = Position;
            Q_EMIT PositionChanged(d_ptr->Position);
        }
        return;
    }

    if (propName == QStringLiteral("ShowTimeout"))
    {
        const uint &ShowTimeout = qvariant_cast<uint>(value);
        if (d_ptr->ShowTimeout != ShowTimeout)
        {
            d_ptr->ShowTimeout = ShowTimeout;
            Q_EMIT ShowTimeoutChanged(d_ptr->ShowTimeout);
        }
        return;
    }

    if (propName == QStringLiteral("WindowMargin"))
    {
        const uint &WindowMargin = qvariant_cast<uint>(value);
        if (d_ptr->WindowMargin != WindowMargin)
        {
            d_ptr->WindowMargin = WindowMargin;
            Q_EMIT WindowMarginChanged(d_ptr->WindowMargin);
        }
        return;
    }

    if (propName == QStringLiteral("WindowSize"))
    {
        const uint &WindowSize = qvariant_cast<uint>(value);
        if (d_ptr->WindowSize != WindowSize)
        {
            d_ptr->WindowSize = WindowSize;
            Q_EMIT WindowSizeChanged(d_ptr->WindowSize);
        }
        return;
    }

    if (propName == QStringLiteral("WindowSizeEfficient"))
    {
        const uint &WindowSizeEfficient = qvariant_cast<uint>(value);
        if (d_ptr->WindowSizeEfficient != WindowSizeEfficient)
        {
            d_ptr->WindowSizeEfficient = WindowSizeEfficient;
            Q_EMIT WindowSizeEfficientChanged(d_ptr->WindowSizeEfficient);
        }
        return;
    }

    if (propName == QStringLiteral("WindowSizeFashion"))
    {
        const uint &WindowSizeFashion = qvariant_cast<uint>(value);
        if (d_ptr->WindowSizeFashion != WindowSizeFashion)
        {
            d_ptr->WindowSizeFashion = WindowSizeFashion;
            Q_EMIT WindowSizeFashionChanged(d_ptr->WindowSizeFashion);
        }
        return;
    }

    qWarning() << "property not handle: " << propName;
    return;
}

int __Dock1::displayMode()
{
    return qvariant_cast<int>(internalPropGet("DisplayMode", &d_ptr->DisplayMode));
}

void __Dock1::setDisplayMode(int value)
{

   internalPropSet("DisplayMode", QVariant::fromValue(value), &d_ptr->DisplayMode);
}

QStringList __Dock1::dockedApps()
{
    return qvariant_cast<QStringList>(internalPropGet("DockedApps", &d_ptr->DockedApps));
}

QList<QDBusObjectPath> __Dock1::entries()
{
    return qvariant_cast<QList<QDBusObjectPath>>(internalPropGet("Entries", &d_ptr->Entries));
}

QRect __Dock1::frontendWindowRect()
{
    return qvariant_cast<QRect>(internalPropGet("FrontendWindowRect", &d_ptr->FrontendWindowRect));
}

int __Dock1::hideMode()
{
    return qvariant_cast<int>(internalPropGet("HideMode", &d_ptr->HideMode));
}

void __Dock1::setHideMode(int value)
{

   internalPropSet("HideMode", QVariant::fromValue(value), &d_ptr->HideMode);
}

int __Dock1::hideState()
{
    return qvariant_cast<int>(internalPropGet("HideState", &d_ptr->HideState));
}

uint __Dock1::hideTimeout()
{
    return qvariant_cast<uint>(internalPropGet("HideTimeout", &d_ptr->HideTimeout));
}

void __Dock1::setHideTimeout(uint value)
{

   internalPropSet("HideTimeout", QVariant::fromValue(value), &d_ptr->HideTimeout);
}

uint __Dock1::iconSize()
{
    return qvariant_cast<uint>(internalPropGet("IconSize", &d_ptr->IconSize));
}

void __Dock1::setIconSize(uint value)
{

   internalPropSet("IconSize", QVariant::fromValue(value), &d_ptr->IconSize);
}

double __Dock1::opacity()
{
    return qvariant_cast<double>(internalPropGet("Opacity", &d_ptr->Opacity));
}

void __Dock1::setOpacity(double value)
{

   internalPropSet("Opacity", QVariant::fromValue(value), &d_ptr->Opacity);
}

int __Dock1::position()
{
    return qvariant_cast<int>(internalPropGet("Position", &d_ptr->Position));
}

void __Dock1::setPosition(int value)
{

   internalPropSet("Position", QVariant::fromValue(value), &d_ptr->Position);
}

uint __Dock1::showTimeout()
{
    return qvariant_cast<uint>(internalPropGet("ShowTimeout", &d_ptr->ShowTimeout));
}

void __Dock1::setShowTimeout(uint value)
{

   internalPropSet("ShowTimeout", QVariant::fromValue(value), &d_ptr->ShowTimeout);
}

uint __Dock1::windowMargin()
{
    return qvariant_cast<uint>(internalPropGet("WindowMargin", &d_ptr->WindowMargin));
}

uint __Dock1::windowSize()
{
    return qvariant_cast<uint>(internalPropGet("WindowSize", &d_ptr->WindowSize));
}

void __Dock1::setWindowSize(uint value)
{

   internalPropSet("WindowSize", QVariant::fromValue(value), &d_ptr->WindowSize);
}

uint __Dock1::windowSizeEfficient()
{
    return qvariant_cast<uint>(internalPropGet("WindowSizeEfficient", &d_ptr->WindowSizeEfficient));
}

void __Dock1::setWindowSizeEfficient(uint value)
{

   internalPropSet("WindowSizeEfficient", QVariant::fromValue(value), &d_ptr->WindowSizeEfficient);
}

uint __Dock1::windowSizeFashion()
{
    return qvariant_cast<uint>(internalPropGet("WindowSizeFashion", &d_ptr->WindowSizeFashion));
}

void __Dock1::setWindowSizeFashion(uint value)
{

   internalPropSet("WindowSizeFashion", QVariant::fromValue(value), &d_ptr->WindowSizeFashion);
}

void __Dock1::CallQueued(const QString &callName, const QList<QVariant> &args)
{
    if (d_ptr->m_waittingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls[callName] = args;
        return;
    }
    if (d_ptr->m_processingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls.insert(callName, args);
    } else {
        QDBusPendingCallWatcher *watcher = new QDBusPendingCallWatcher(asyncCallWithArgumentList(callName, args));
        connect(watcher, &QDBusPendingCallWatcher::finished, this, &__Dock1::onPendingCallFinished);
        d_ptr->m_processingCalls.insert(callName, watcher);
    }
}

void __Dock1::onPendingCallFinished(QDBusPendingCallWatcher *w)
{
    w->deleteLater();
    const auto callName = d_ptr->m_processingCalls.key(w);
    Q_ASSERT(!callName.isEmpty());
    if (callName.isEmpty())
        return;
    d_ptr->m_processingCalls.remove(callName);
    if (!d_ptr->m_waittingCalls.contains(callName))
        return;
    const auto args = d_ptr->m_waittingCalls.take(callName);
    CallQueued(callName, args);
}

#include "moc_DaemonDock1.cpp"
