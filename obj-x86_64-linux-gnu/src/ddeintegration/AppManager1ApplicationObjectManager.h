/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c AppManager1ApplicationObjectManager -i types/amglobaltypes.h -p AppManager1ApplicationObjectManager /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ObjectManager1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef APPMANAGER1APPLICATIONOBJECTMANAGER_H
#define APPMANAGER1APPLICATIONOBJECTMANAGER_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

#include <DDBusExtendedAbstractInterface>
#include <QtDBus/QtDBus>
#include "types/amglobaltypes.h"

#include "types/objectmap.h"

/*
 * Proxy class for interface org.desktopspec.DBus.ObjectManager
 */
class __AppManager1ApplicationObjectManagerPrivate;
class __AppManager1ApplicationObjectManager : public DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface
{
    Q_OBJECT

public:
    static inline const char *staticInterfaceName()
    { return "org.desktopspec.DBus.ObjectManager"; }

public:
    explicit __AppManager1ApplicationObjectManager(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = 0);

    ~__AppManager1ApplicationObjectManager();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<ObjectMap> GetManagedObjects()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetManagedObjects"), argumentList);
    }




Q_SIGNALS: // SIGNALS
    void InterfacesAdded(const QDBusObjectPath &object_path, ObjectInterfaceMap interfaces_and_properties);
    void InterfacesRemoved(const QDBusObjectPath &object_path, const QStringList &interfaces);
    // begin property changed signals

public Q_SLOTS:
    void CallQueued(const QString &callName, const QList<QVariant> &args);

private Q_SLOTS:
    void onPendingCallFinished(QDBusPendingCallWatcher *w);

private:
    __AppManager1ApplicationObjectManagerPrivate *d_ptr;
};

#endif
