/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c Appearance1 -p Appearance1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.Appearance1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef APPEARANCE1_H
#define APPEARANCE1_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

#include <DDBusExtendedAbstractInterface>
#include <QtDBus/QtDBus>


/*
 * Proxy class for interface org.deepin.dde.Appearance1
 */
class __Appearance1Private;
class __Appearance1 : public DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface
{
    Q_OBJECT

public:
    static inline const char *staticInterfaceName()
    { return "org.deepin.dde.Appearance1"; }

public:
    explicit __Appearance1(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = 0);

    ~__Appearance1();

    Q_PROPERTY(QString Background READ background WRITE setBackground NOTIFY BackgroundChanged)
    QString background();
    void setBackground(const QString &value);

    Q_PROPERTY(QString CursorTheme READ cursorTheme WRITE setCursorTheme NOTIFY CursorThemeChanged)
    QString cursorTheme();
    void setCursorTheme(const QString &value);

    Q_PROPERTY(double FontSize READ fontSize WRITE setFontSize NOTIFY FontSizeChanged)
    double fontSize();
    void setFontSize(double value);

    Q_PROPERTY(QString GlobalTheme READ globalTheme NOTIFY GlobalThemeChanged)
    QString globalTheme();

    Q_PROPERTY(QString GtkTheme READ gtkTheme WRITE setGtkTheme NOTIFY GtkThemeChanged)
    QString gtkTheme();
    void setGtkTheme(const QString &value);

    Q_PROPERTY(QString IconTheme READ iconTheme WRITE setIconTheme NOTIFY IconThemeChanged)
    QString iconTheme();
    void setIconTheme(const QString &value);

    Q_PROPERTY(QString MonospaceFont READ monospaceFont WRITE setMonospaceFont NOTIFY MonospaceFontChanged)
    QString monospaceFont();
    void setMonospaceFont(const QString &value);

    Q_PROPERTY(double Opacity READ opacity WRITE setOpacity NOTIFY OpacityChanged)
    double opacity();
    void setOpacity(double value);

    Q_PROPERTY(QString QtActiveColor READ qtActiveColor WRITE setQtActiveColor NOTIFY QtActiveColorChanged)
    QString qtActiveColor();
    void setQtActiveColor(const QString &value);

    Q_PROPERTY(QString StandardFont READ standardFont WRITE setStandardFont NOTIFY StandardFontChanged)
    QString standardFont();
    void setStandardFont(const QString &value);

    Q_PROPERTY(QString WallpaperSlideShow READ wallpaperSlideShow WRITE setWallpaperSlideShow NOTIFY WallpaperSlideShowChanged)
    QString wallpaperSlideShow();
    void setWallpaperSlideShow(const QString &value);

    Q_PROPERTY(QString WallpaperURls READ wallpaperURls NOTIFY WallpaperURlsChanged)
    QString wallpaperURls();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<> Delete(const QString &in0, const QString &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("Delete"), argumentList);
    }

    inline void DeleteQueued(const QString &in0, const QString &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);

        CallQueued(QStringLiteral("Delete"), argumentList);
    }


    inline QDBusPendingReply<QString> GetCurrentWorkspaceBackgroundForMonitor(const QString &strMonitorName)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(strMonitorName);
        return asyncCallWithArgumentList(QStringLiteral("GetCurrentWorkspaceBackgroundForMonitor"), argumentList);
    }



    inline QDBusPendingReply<double> GetScaleFactor()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetScaleFactor"), argumentList);
    }



    inline QDBusPendingReply<QMap<QString,double> > GetScreenScaleFactors()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("GetScreenScaleFactors"), argumentList);
    }



    inline QDBusPendingReply<QString> List(const QString &in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("List"), argumentList);
    }



    inline QDBusPendingReply<> Set(const QString &in0, const QString &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("Set"), argumentList);
    }

    inline void SetQueued(const QString &in0, const QString &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);

        CallQueued(QStringLiteral("Set"), argumentList);
    }


    inline QDBusPendingReply<> SetScaleFactor(double in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);
        return asyncCallWithArgumentList(QStringLiteral("SetScaleFactor"), argumentList);
    }

    inline void SetScaleFactorQueued(double in0)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0);

        CallQueued(QStringLiteral("SetScaleFactor"), argumentList);
    }


    inline QDBusPendingReply<> SetScreenScaleFactors(const QMap<QString,double> &scaleFactors)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(scaleFactors);
        return asyncCallWithArgumentList(QStringLiteral("SetScreenScaleFactors"), argumentList);
    }

    inline void SetScreenScaleFactorsQueued(const QMap<QString,double> &scaleFactors)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(scaleFactors);

        CallQueued(QStringLiteral("SetScreenScaleFactors"), argumentList);
    }


    inline QDBusPendingReply<QString> Show(const QString &in0, const QStringList &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("Show"), argumentList);
    }



    inline QDBusPendingReply<QString> Thumbnail(const QString &in0, const QString &in1)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(in0) << QVariant::fromValue(in1);
        return asyncCallWithArgumentList(QStringLiteral("Thumbnail"), argumentList);
    }




Q_SIGNALS: // SIGNALS
    void Changed(const QString &in0, const QString &in1);
    void Refreshed(const QString &in0);
    // begin property changed signals
    void BackgroundChanged(const QString & value) const;
    void CursorThemeChanged(const QString & value) const;
    void FontSizeChanged(double  value) const;
    void GlobalThemeChanged(const QString & value) const;
    void GtkThemeChanged(const QString & value) const;
    void IconThemeChanged(const QString & value) const;
    void MonospaceFontChanged(const QString & value) const;
    void OpacityChanged(double  value) const;
    void QtActiveColorChanged(const QString & value) const;
    void StandardFontChanged(const QString & value) const;
    void WallpaperSlideShowChanged(const QString & value) const;
    void WallpaperURlsChanged(const QString & value) const;

public Q_SLOTS:
    void CallQueued(const QString &callName, const QList<QVariant> &args);

private Q_SLOTS:
    void onPendingCallFinished(QDBusPendingCallWatcher *w);
    void onPropertyChanged(const QString &propName, const QVariant &value);

private:
    __Appearance1Private *d_ptr;
};

#endif
