/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c AppManager1Application -i types/amglobaltypes.h -p AppManager1Application /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "AppManager1Application.h"

DCORE_USE_NAMESPACE
/*
 * Implementation of interface class __AppManager1Application
 */

class __AppManager1ApplicationPrivate
{
public:
   __AppManager1ApplicationPrivate() = default;

    // begin member variables
    PropMap ActionName;
    QStringList Actions;
    bool AutoStart;
    QStringList Categories;
    QString Environ;
    QStringMap GenericName;
    QString ID;
    QStringMap Icons;
    qlonglong InstalledTime;
    QList<QDBusObjectPath> Instances;
    qlonglong LastLaunchedTime;
    qlonglong LaunchedTimes;
    QStringList MimeTypes;
    QStringMap Name;
    bool NoDisplay;
    bool Terminal;
    QString X_Deepin_Vendor;
    bool X_Flatpak;
    bool X_linglong;
    bool isOnDesktop;

public:
    QMap<QString, QDBusPendingCallWatcher *> m_processingCalls;
    QMap<QString, QList<QVariant>> m_waittingCalls;
};

__AppManager1Application::__AppManager1Application(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent)
    : DDBusExtendedAbstractInterface(service, path, staticInterfaceName(), connection, parent)
    , d_ptr(new __AppManager1ApplicationPrivate)
{
    connect(this, &__AppManager1Application::propertyChanged, this, &__AppManager1Application::onPropertyChanged);

    if (QMetaType::fromName("QVariantMap").id() == QMetaType::UnknownType)
        registerQVariantMapMetaType();
    if (QMetaType::fromName("PropMap").id() == QMetaType::UnknownType)
        registerPropMapMetaType();
    if (QMetaType::fromName("QStringMap").id() == QMetaType::UnknownType)
        registerQStringMapMetaType();
}

__AppManager1Application::~__AppManager1Application()
{
    qDeleteAll(d_ptr->m_processingCalls.values());
    delete d_ptr;
}

void __AppManager1Application::onPropertyChanged(const QString &propName, const QVariant &value)
{
    if (propName == QStringLiteral("ActionName"))
    {
        const PropMap &ActionName = qvariant_cast<PropMap>(value);
        if (d_ptr->ActionName != ActionName)
        {
            d_ptr->ActionName = ActionName;
            Q_EMIT ActionNameChanged(d_ptr->ActionName);
        }
        return;
    }

    if (propName == QStringLiteral("Actions"))
    {
        const QStringList &Actions = qvariant_cast<QStringList>(value);
        if (d_ptr->Actions != Actions)
        {
            d_ptr->Actions = Actions;
            Q_EMIT ActionsChanged(d_ptr->Actions);
        }
        return;
    }

    if (propName == QStringLiteral("AutoStart"))
    {
        const bool &AutoStart = qvariant_cast<bool>(value);
        if (d_ptr->AutoStart != AutoStart)
        {
            d_ptr->AutoStart = AutoStart;
            Q_EMIT AutoStartChanged(d_ptr->AutoStart);
        }
        return;
    }

    if (propName == QStringLiteral("Categories"))
    {
        const QStringList &Categories = qvariant_cast<QStringList>(value);
        if (d_ptr->Categories != Categories)
        {
            d_ptr->Categories = Categories;
            Q_EMIT CategoriesChanged(d_ptr->Categories);
        }
        return;
    }

    if (propName == QStringLiteral("Environ"))
    {
        const QString &Environ = qvariant_cast<QString>(value);
        if (d_ptr->Environ != Environ)
        {
            d_ptr->Environ = Environ;
            Q_EMIT EnvironChanged(d_ptr->Environ);
        }
        return;
    }

    if (propName == QStringLiteral("GenericName"))
    {
        const QStringMap &GenericName = qvariant_cast<QStringMap>(value);
        if (d_ptr->GenericName != GenericName)
        {
            d_ptr->GenericName = GenericName;
            Q_EMIT GenericNameChanged(d_ptr->GenericName);
        }
        return;
    }

    if (propName == QStringLiteral("ID"))
    {
        const QString &ID = qvariant_cast<QString>(value);
        if (d_ptr->ID != ID)
        {
            d_ptr->ID = ID;
            Q_EMIT IDChanged(d_ptr->ID);
        }
        return;
    }

    if (propName == QStringLiteral("Icons"))
    {
        const QStringMap &Icons = qvariant_cast<QStringMap>(value);
        if (d_ptr->Icons != Icons)
        {
            d_ptr->Icons = Icons;
            Q_EMIT IconsChanged(d_ptr->Icons);
        }
        return;
    }

    if (propName == QStringLiteral("InstalledTime"))
    {
        const qlonglong &InstalledTime = qvariant_cast<qlonglong>(value);
        if (d_ptr->InstalledTime != InstalledTime)
        {
            d_ptr->InstalledTime = InstalledTime;
            Q_EMIT InstalledTimeChanged(d_ptr->InstalledTime);
        }
        return;
    }

    if (propName == QStringLiteral("Instances"))
    {
        const QList<QDBusObjectPath> &Instances = qvariant_cast<QList<QDBusObjectPath>>(value);
        if (d_ptr->Instances != Instances)
        {
            d_ptr->Instances = Instances;
            Q_EMIT InstancesChanged(d_ptr->Instances);
        }
        return;
    }

    if (propName == QStringLiteral("LastLaunchedTime"))
    {
        const qlonglong &LastLaunchedTime = qvariant_cast<qlonglong>(value);
        if (d_ptr->LastLaunchedTime != LastLaunchedTime)
        {
            d_ptr->LastLaunchedTime = LastLaunchedTime;
            Q_EMIT LastLaunchedTimeChanged(d_ptr->LastLaunchedTime);
        }
        return;
    }

    if (propName == QStringLiteral("LaunchedTimes"))
    {
        const qlonglong &LaunchedTimes = qvariant_cast<qlonglong>(value);
        if (d_ptr->LaunchedTimes != LaunchedTimes)
        {
            d_ptr->LaunchedTimes = LaunchedTimes;
            Q_EMIT LaunchedTimesChanged(d_ptr->LaunchedTimes);
        }
        return;
    }

    if (propName == QStringLiteral("MimeTypes"))
    {
        const QStringList &MimeTypes = qvariant_cast<QStringList>(value);
        if (d_ptr->MimeTypes != MimeTypes)
        {
            d_ptr->MimeTypes = MimeTypes;
            Q_EMIT MimeTypesChanged(d_ptr->MimeTypes);
        }
        return;
    }

    if (propName == QStringLiteral("Name"))
    {
        const QStringMap &Name = qvariant_cast<QStringMap>(value);
        if (d_ptr->Name != Name)
        {
            d_ptr->Name = Name;
            Q_EMIT NameChanged(d_ptr->Name);
        }
        return;
    }

    if (propName == QStringLiteral("NoDisplay"))
    {
        const bool &NoDisplay = qvariant_cast<bool>(value);
        if (d_ptr->NoDisplay != NoDisplay)
        {
            d_ptr->NoDisplay = NoDisplay;
            Q_EMIT NoDisplayChanged(d_ptr->NoDisplay);
        }
        return;
    }

    if (propName == QStringLiteral("Terminal"))
    {
        const bool &Terminal = qvariant_cast<bool>(value);
        if (d_ptr->Terminal != Terminal)
        {
            d_ptr->Terminal = Terminal;
            Q_EMIT TerminalChanged(d_ptr->Terminal);
        }
        return;
    }

    if (propName == QStringLiteral("X_Deepin_Vendor"))
    {
        const QString &X_Deepin_Vendor = qvariant_cast<QString>(value);
        if (d_ptr->X_Deepin_Vendor != X_Deepin_Vendor)
        {
            d_ptr->X_Deepin_Vendor = X_Deepin_Vendor;
            Q_EMIT X_Deepin_VendorChanged(d_ptr->X_Deepin_Vendor);
        }
        return;
    }

    if (propName == QStringLiteral("X_Flatpak"))
    {
        const bool &X_Flatpak = qvariant_cast<bool>(value);
        if (d_ptr->X_Flatpak != X_Flatpak)
        {
            d_ptr->X_Flatpak = X_Flatpak;
            Q_EMIT X_FlatpakChanged(d_ptr->X_Flatpak);
        }
        return;
    }

    if (propName == QStringLiteral("X_linglong"))
    {
        const bool &X_linglong = qvariant_cast<bool>(value);
        if (d_ptr->X_linglong != X_linglong)
        {
            d_ptr->X_linglong = X_linglong;
            Q_EMIT X_linglongChanged(d_ptr->X_linglong);
        }
        return;
    }

    if (propName == QStringLiteral("isOnDesktop"))
    {
        const bool &isOnDesktop = qvariant_cast<bool>(value);
        if (d_ptr->isOnDesktop != isOnDesktop)
        {
            d_ptr->isOnDesktop = isOnDesktop;
            Q_EMIT IsOnDesktopChanged(d_ptr->isOnDesktop);
        }
        return;
    }

    qWarning() << "property not handle: " << propName;
    return;
}

PropMap __AppManager1Application::actionName()
{
    return qvariant_cast<PropMap>(internalPropGet("ActionName", &d_ptr->ActionName));
}

QStringList __AppManager1Application::actions()
{
    return qvariant_cast<QStringList>(internalPropGet("Actions", &d_ptr->Actions));
}

bool __AppManager1Application::autoStart()
{
    return qvariant_cast<bool>(internalPropGet("AutoStart", &d_ptr->AutoStart));
}

void __AppManager1Application::setAutoStart(bool value)
{

   internalPropSet("AutoStart", QVariant::fromValue(value), &d_ptr->AutoStart);
}

QStringList __AppManager1Application::categories()
{
    return qvariant_cast<QStringList>(internalPropGet("Categories", &d_ptr->Categories));
}

QString __AppManager1Application::environ()
{
    return qvariant_cast<QString>(internalPropGet("Environ", &d_ptr->Environ));
}

void __AppManager1Application::setEnviron(const QString &value)
{

   internalPropSet("Environ", QVariant::fromValue(value), &d_ptr->Environ);
}

QStringMap __AppManager1Application::genericName()
{
    return qvariant_cast<QStringMap>(internalPropGet("GenericName", &d_ptr->GenericName));
}

QString __AppManager1Application::iD()
{
    return qvariant_cast<QString>(internalPropGet("ID", &d_ptr->ID));
}

QStringMap __AppManager1Application::icons()
{
    return qvariant_cast<QStringMap>(internalPropGet("Icons", &d_ptr->Icons));
}

qlonglong __AppManager1Application::installedTime()
{
    return qvariant_cast<qlonglong>(internalPropGet("InstalledTime", &d_ptr->InstalledTime));
}

QList<QDBusObjectPath> __AppManager1Application::instances()
{
    return qvariant_cast<QList<QDBusObjectPath>>(internalPropGet("Instances", &d_ptr->Instances));
}

qlonglong __AppManager1Application::lastLaunchedTime()
{
    return qvariant_cast<qlonglong>(internalPropGet("LastLaunchedTime", &d_ptr->LastLaunchedTime));
}

qlonglong __AppManager1Application::launchedTimes()
{
    return qvariant_cast<qlonglong>(internalPropGet("LaunchedTimes", &d_ptr->LaunchedTimes));
}

QStringList __AppManager1Application::mimeTypes()
{
    return qvariant_cast<QStringList>(internalPropGet("MimeTypes", &d_ptr->MimeTypes));
}

void __AppManager1Application::setMimeTypes(const QStringList &value)
{

   internalPropSet("MimeTypes", QVariant::fromValue(value), &d_ptr->MimeTypes);
}

QStringMap __AppManager1Application::name()
{
    return qvariant_cast<QStringMap>(internalPropGet("Name", &d_ptr->Name));
}

bool __AppManager1Application::noDisplay()
{
    return qvariant_cast<bool>(internalPropGet("NoDisplay", &d_ptr->NoDisplay));
}

bool __AppManager1Application::terminal()
{
    return qvariant_cast<bool>(internalPropGet("Terminal", &d_ptr->Terminal));
}

QString __AppManager1Application::x_Deepin_Vendor()
{
    return qvariant_cast<QString>(internalPropGet("X_Deepin_Vendor", &d_ptr->X_Deepin_Vendor));
}

bool __AppManager1Application::x_Flatpak()
{
    return qvariant_cast<bool>(internalPropGet("X_Flatpak", &d_ptr->X_Flatpak));
}

bool __AppManager1Application::x_linglong()
{
    return qvariant_cast<bool>(internalPropGet("X_linglong", &d_ptr->X_linglong));
}

bool __AppManager1Application::isOnDesktop()
{
    return qvariant_cast<bool>(internalPropGet("isOnDesktop", &d_ptr->isOnDesktop));
}

void __AppManager1Application::CallQueued(const QString &callName, const QList<QVariant> &args)
{
    if (d_ptr->m_waittingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls[callName] = args;
        return;
    }
    if (d_ptr->m_processingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls.insert(callName, args);
    } else {
        QDBusPendingCallWatcher *watcher = new QDBusPendingCallWatcher(asyncCallWithArgumentList(callName, args));
        connect(watcher, &QDBusPendingCallWatcher::finished, this, &__AppManager1Application::onPendingCallFinished);
        d_ptr->m_processingCalls.insert(callName, watcher);
    }
}

void __AppManager1Application::onPendingCallFinished(QDBusPendingCallWatcher *w)
{
    w->deleteLater();
    const auto callName = d_ptr->m_processingCalls.key(w);
    Q_ASSERT(!callName.isEmpty());
    if (callName.isEmpty())
        return;
    d_ptr->m_processingCalls.remove(callName);
    if (!d_ptr->m_waittingCalls.contains(callName))
        return;
    const auto args = d_ptr->m_waittingCalls.take(callName);
    CallQueued(callName, args);
}

#include "moc_AppManager1Application.cpp"
