/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c Appearance1 -p Appearance1 /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.deepin.dde.Appearance1.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#include "Appearance1.h"

DCORE_USE_NAMESPACE
/*
 * Implementation of interface class __Appearance1
 */

class __Appearance1Private
{
public:
   __Appearance1Private() = default;

    // begin member variables
    QString Background;
    QString CursorTheme;
    double FontSize;
    QString GlobalTheme;
    QString GtkTheme;
    QString IconTheme;
    QString MonospaceFont;
    double Opacity;
    QString QtActiveColor;
    QString StandardFont;
    QString WallpaperSlideShow;
    QString WallpaperURls;

public:
    QMap<QString, QDBusPendingCallWatcher *> m_processingCalls;
    QMap<QString, QList<QVariant>> m_waittingCalls;
};

__Appearance1::__Appearance1(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent)
    : DDBusExtendedAbstractInterface(service, path, staticInterfaceName(), connection, parent)
    , d_ptr(new __Appearance1Private)
{
    connect(this, &__Appearance1::propertyChanged, this, &__Appearance1::onPropertyChanged);

    if (QMetaType::fromName("QMap<QString,double>").id() == QMetaType::UnknownType) {
        qRegisterMetaType< QMap<QString,double> >("QMap<QString,double>");
        qDBusRegisterMetaType< QMap<QString,double> >();
    }
}

__Appearance1::~__Appearance1()
{
    qDeleteAll(d_ptr->m_processingCalls.values());
    delete d_ptr;
}

void __Appearance1::onPropertyChanged(const QString &propName, const QVariant &value)
{
    if (propName == QStringLiteral("Background"))
    {
        const QString &Background = qvariant_cast<QString>(value);
        if (d_ptr->Background != Background)
        {
            d_ptr->Background = Background;
            Q_EMIT BackgroundChanged(d_ptr->Background);
        }
        return;
    }

    if (propName == QStringLiteral("CursorTheme"))
    {
        const QString &CursorTheme = qvariant_cast<QString>(value);
        if (d_ptr->CursorTheme != CursorTheme)
        {
            d_ptr->CursorTheme = CursorTheme;
            Q_EMIT CursorThemeChanged(d_ptr->CursorTheme);
        }
        return;
    }

    if (propName == QStringLiteral("FontSize"))
    {
        const double &FontSize = qvariant_cast<double>(value);
        if (d_ptr->FontSize != FontSize)
        {
            d_ptr->FontSize = FontSize;
            Q_EMIT FontSizeChanged(d_ptr->FontSize);
        }
        return;
    }

    if (propName == QStringLiteral("GlobalTheme"))
    {
        const QString &GlobalTheme = qvariant_cast<QString>(value);
        if (d_ptr->GlobalTheme != GlobalTheme)
        {
            d_ptr->GlobalTheme = GlobalTheme;
            Q_EMIT GlobalThemeChanged(d_ptr->GlobalTheme);
        }
        return;
    }

    if (propName == QStringLiteral("GtkTheme"))
    {
        const QString &GtkTheme = qvariant_cast<QString>(value);
        if (d_ptr->GtkTheme != GtkTheme)
        {
            d_ptr->GtkTheme = GtkTheme;
            Q_EMIT GtkThemeChanged(d_ptr->GtkTheme);
        }
        return;
    }

    if (propName == QStringLiteral("IconTheme"))
    {
        const QString &IconTheme = qvariant_cast<QString>(value);
        if (d_ptr->IconTheme != IconTheme)
        {
            d_ptr->IconTheme = IconTheme;
            Q_EMIT IconThemeChanged(d_ptr->IconTheme);
        }
        return;
    }

    if (propName == QStringLiteral("MonospaceFont"))
    {
        const QString &MonospaceFont = qvariant_cast<QString>(value);
        if (d_ptr->MonospaceFont != MonospaceFont)
        {
            d_ptr->MonospaceFont = MonospaceFont;
            Q_EMIT MonospaceFontChanged(d_ptr->MonospaceFont);
        }
        return;
    }

    if (propName == QStringLiteral("Opacity"))
    {
        const double &Opacity = qvariant_cast<double>(value);
        if (d_ptr->Opacity != Opacity)
        {
            d_ptr->Opacity = Opacity;
            Q_EMIT OpacityChanged(d_ptr->Opacity);
        }
        return;
    }

    if (propName == QStringLiteral("QtActiveColor"))
    {
        const QString &QtActiveColor = qvariant_cast<QString>(value);
        if (d_ptr->QtActiveColor != QtActiveColor)
        {
            d_ptr->QtActiveColor = QtActiveColor;
            Q_EMIT QtActiveColorChanged(d_ptr->QtActiveColor);
        }
        return;
    }

    if (propName == QStringLiteral("StandardFont"))
    {
        const QString &StandardFont = qvariant_cast<QString>(value);
        if (d_ptr->StandardFont != StandardFont)
        {
            d_ptr->StandardFont = StandardFont;
            Q_EMIT StandardFontChanged(d_ptr->StandardFont);
        }
        return;
    }

    if (propName == QStringLiteral("WallpaperSlideShow"))
    {
        const QString &WallpaperSlideShow = qvariant_cast<QString>(value);
        if (d_ptr->WallpaperSlideShow != WallpaperSlideShow)
        {
            d_ptr->WallpaperSlideShow = WallpaperSlideShow;
            Q_EMIT WallpaperSlideShowChanged(d_ptr->WallpaperSlideShow);
        }
        return;
    }

    if (propName == QStringLiteral("WallpaperURls"))
    {
        const QString &WallpaperURls = qvariant_cast<QString>(value);
        if (d_ptr->WallpaperURls != WallpaperURls)
        {
            d_ptr->WallpaperURls = WallpaperURls;
            Q_EMIT WallpaperURlsChanged(d_ptr->WallpaperURls);
        }
        return;
    }

    qWarning() << "property not handle: " << propName;
    return;
}

QString __Appearance1::background()
{
    return qvariant_cast<QString>(internalPropGet("Background", &d_ptr->Background));
}

void __Appearance1::setBackground(const QString &value)
{

   internalPropSet("Background", QVariant::fromValue(value), &d_ptr->Background);
}

QString __Appearance1::cursorTheme()
{
    return qvariant_cast<QString>(internalPropGet("CursorTheme", &d_ptr->CursorTheme));
}

void __Appearance1::setCursorTheme(const QString &value)
{

   internalPropSet("CursorTheme", QVariant::fromValue(value), &d_ptr->CursorTheme);
}

double __Appearance1::fontSize()
{
    return qvariant_cast<double>(internalPropGet("FontSize", &d_ptr->FontSize));
}

void __Appearance1::setFontSize(double value)
{

   internalPropSet("FontSize", QVariant::fromValue(value), &d_ptr->FontSize);
}

QString __Appearance1::globalTheme()
{
    return qvariant_cast<QString>(internalPropGet("GlobalTheme", &d_ptr->GlobalTheme));
}

QString __Appearance1::gtkTheme()
{
    return qvariant_cast<QString>(internalPropGet("GtkTheme", &d_ptr->GtkTheme));
}

void __Appearance1::setGtkTheme(const QString &value)
{

   internalPropSet("GtkTheme", QVariant::fromValue(value), &d_ptr->GtkTheme);
}

QString __Appearance1::iconTheme()
{
    return qvariant_cast<QString>(internalPropGet("IconTheme", &d_ptr->IconTheme));
}

void __Appearance1::setIconTheme(const QString &value)
{

   internalPropSet("IconTheme", QVariant::fromValue(value), &d_ptr->IconTheme);
}

QString __Appearance1::monospaceFont()
{
    return qvariant_cast<QString>(internalPropGet("MonospaceFont", &d_ptr->MonospaceFont));
}

void __Appearance1::setMonospaceFont(const QString &value)
{

   internalPropSet("MonospaceFont", QVariant::fromValue(value), &d_ptr->MonospaceFont);
}

double __Appearance1::opacity()
{
    return qvariant_cast<double>(internalPropGet("Opacity", &d_ptr->Opacity));
}

void __Appearance1::setOpacity(double value)
{

   internalPropSet("Opacity", QVariant::fromValue(value), &d_ptr->Opacity);
}

QString __Appearance1::qtActiveColor()
{
    return qvariant_cast<QString>(internalPropGet("QtActiveColor", &d_ptr->QtActiveColor));
}

void __Appearance1::setQtActiveColor(const QString &value)
{

   internalPropSet("QtActiveColor", QVariant::fromValue(value), &d_ptr->QtActiveColor);
}

QString __Appearance1::standardFont()
{
    return qvariant_cast<QString>(internalPropGet("StandardFont", &d_ptr->StandardFont));
}

void __Appearance1::setStandardFont(const QString &value)
{

   internalPropSet("StandardFont", QVariant::fromValue(value), &d_ptr->StandardFont);
}

QString __Appearance1::wallpaperSlideShow()
{
    return qvariant_cast<QString>(internalPropGet("WallpaperSlideShow", &d_ptr->WallpaperSlideShow));
}

void __Appearance1::setWallpaperSlideShow(const QString &value)
{

   internalPropSet("WallpaperSlideShow", QVariant::fromValue(value), &d_ptr->WallpaperSlideShow);
}

QString __Appearance1::wallpaperURls()
{
    return qvariant_cast<QString>(internalPropGet("WallpaperURls", &d_ptr->WallpaperURls));
}

void __Appearance1::CallQueued(const QString &callName, const QList<QVariant> &args)
{
    if (d_ptr->m_waittingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls[callName] = args;
        return;
    }
    if (d_ptr->m_processingCalls.contains(callName))
    {
        d_ptr->m_waittingCalls.insert(callName, args);
    } else {
        QDBusPendingCallWatcher *watcher = new QDBusPendingCallWatcher(asyncCallWithArgumentList(callName, args));
        connect(watcher, &QDBusPendingCallWatcher::finished, this, &__Appearance1::onPendingCallFinished);
        d_ptr->m_processingCalls.insert(callName, watcher);
    }
}

void __Appearance1::onPendingCallFinished(QDBusPendingCallWatcher *w)
{
    w->deleteLater();
    const auto callName = d_ptr->m_processingCalls.key(w);
    Q_ASSERT(!callName.isEmpty());
    if (callName.isEmpty())
        return;
    d_ptr->m_processingCalls.remove(callName);
    if (!d_ptr->m_waittingCalls.contains(callName))
        return;
    const auto args = d_ptr->m_waittingCalls.take(callName);
    CallQueued(callName, args);
}

#include "moc_Appearance1.cpp"
