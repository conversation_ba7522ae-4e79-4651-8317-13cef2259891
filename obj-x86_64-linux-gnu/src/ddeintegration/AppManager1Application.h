/*
 * This file was generated by qdbusxml2cpp-fix version 0.8
 * Command line was: qdbusxml2cpp-fix -N -m -c AppManager1Application -i types/amglobaltypes.h -p AppManager1Application /home/<USER>/Desktop/myrepo/dde-launchpad/src/ddeintegration/xml/org.desktopspec.ApplicationManager1.Application.xml
 *
 * qdbusxml2cpp-fix is Copyright (C) 2016 Deepin Technology Co., Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#ifndef APPMANAGER1APPLICATION_H
#define APPMANAGER1APPLICATION_H

#include <QtCore/QObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

#include <DDBusExtendedAbstractInterface>
#include <QtDBus/QtDBus>
#include "types/amglobaltypes.h"

#include "types/qvariantmap.h"
#include "types/propmap.h"
#include "types/qstringmap.h"

/*
 * Proxy class for interface org.desktopspec.ApplicationManager1.Application
 */
class __AppManager1ApplicationPrivate;
class __AppManager1Application : public DTK_CORE_NAMESPACE::DDBusExtendedAbstractInterface
{
    Q_OBJECT

public:
    static inline const char *staticInterfaceName()
    { return "org.desktopspec.ApplicationManager1.Application"; }

public:
    explicit __AppManager1Application(const QString &service, const QString &path, const QDBusConnection &connection, QObject *parent = 0);

    ~__AppManager1Application();

    Q_PROPERTY(PropMap ActionName READ actionName NOTIFY ActionNameChanged)
    PropMap actionName();

    Q_PROPERTY(QStringList Actions READ actions NOTIFY ActionsChanged)
    QStringList actions();

    Q_PROPERTY(bool AutoStart READ autoStart WRITE setAutoStart NOTIFY AutoStartChanged)
    bool autoStart();
    void setAutoStart(bool value);

    Q_PROPERTY(QStringList Categories READ categories NOTIFY CategoriesChanged)
    QStringList categories();

    Q_PROPERTY(QString Environ READ environ WRITE setEnviron NOTIFY EnvironChanged)
    QString environ();
    void setEnviron(const QString &value);

    Q_PROPERTY(QStringMap GenericName READ genericName NOTIFY GenericNameChanged)
    QStringMap genericName();

    Q_PROPERTY(QString ID READ iD NOTIFY IDChanged)
    QString iD();

    Q_PROPERTY(QStringMap Icons READ icons NOTIFY IconsChanged)
    QStringMap icons();

    Q_PROPERTY(qlonglong InstalledTime READ installedTime NOTIFY InstalledTimeChanged)
    qlonglong installedTime();

    Q_PROPERTY(QList<QDBusObjectPath> Instances READ instances NOTIFY InstancesChanged)
    QList<QDBusObjectPath> instances();

    Q_PROPERTY(qlonglong LastLaunchedTime READ lastLaunchedTime NOTIFY LastLaunchedTimeChanged)
    qlonglong lastLaunchedTime();

    Q_PROPERTY(qlonglong LaunchedTimes READ launchedTimes NOTIFY LaunchedTimesChanged)
    qlonglong launchedTimes();

    Q_PROPERTY(QStringList MimeTypes READ mimeTypes WRITE setMimeTypes NOTIFY MimeTypesChanged)
    QStringList mimeTypes();
    void setMimeTypes(const QStringList &value);

    Q_PROPERTY(QStringMap Name READ name NOTIFY NameChanged)
    QStringMap name();

    Q_PROPERTY(bool NoDisplay READ noDisplay NOTIFY NoDisplayChanged)
    bool noDisplay();

    Q_PROPERTY(bool Terminal READ terminal NOTIFY TerminalChanged)
    bool terminal();

    Q_PROPERTY(QString X_Deepin_Vendor READ x_Deepin_Vendor NOTIFY X_Deepin_VendorChanged)
    QString x_Deepin_Vendor();

    Q_PROPERTY(bool X_Flatpak READ x_Flatpak NOTIFY X_FlatpakChanged)
    bool x_Flatpak();

    Q_PROPERTY(bool X_linglong READ x_linglong NOTIFY X_linglongChanged)
    bool x_linglong();

    Q_PROPERTY(bool isOnDesktop READ isOnDesktop NOTIFY IsOnDesktopChanged)
    bool isOnDesktop();

public Q_SLOTS: // METHODS
    inline QDBusPendingReply<QDBusObjectPath> Launch(const QString &action, const QStringList &fields, const QVariantMap &options)
    {
        QList<QVariant> argumentList;
        argumentList << QVariant::fromValue(action) << QVariant::fromValue(fields) << QVariant::fromValue(options);
        return asyncCallWithArgumentList(QStringLiteral("Launch"), argumentList);
    }



    inline QDBusPendingReply<bool> RemoveFromDesktop()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("RemoveFromDesktop"), argumentList);
    }



    inline QDBusPendingReply<bool> SendToDesktop()
    {
        QList<QVariant> argumentList;
        return asyncCallWithArgumentList(QStringLiteral("SendToDesktop"), argumentList);
    }




Q_SIGNALS: // SIGNALS
    // begin property changed signals
    void ActionNameChanged(PropMap  value) const;
    void ActionsChanged(const QStringList & value) const;
    void AutoStartChanged(bool  value) const;
    void CategoriesChanged(const QStringList & value) const;
    void EnvironChanged(const QString & value) const;
    void GenericNameChanged(const QStringMap & value) const;
    void IDChanged(const QString & value) const;
    void IconsChanged(const QStringMap & value) const;
    void InstalledTimeChanged(qlonglong  value) const;
    void InstancesChanged(const QList<QDBusObjectPath> & value) const;
    void LastLaunchedTimeChanged(qlonglong  value) const;
    void LaunchedTimesChanged(qlonglong  value) const;
    void MimeTypesChanged(const QStringList & value) const;
    void NameChanged(const QStringMap & value) const;
    void NoDisplayChanged(bool  value) const;
    void TerminalChanged(bool  value) const;
    void X_Deepin_VendorChanged(const QString & value) const;
    void X_FlatpakChanged(bool  value) const;
    void X_linglongChanged(bool  value) const;
    void IsOnDesktopChanged(bool  value) const;

public Q_SLOTS:
    void CallQueued(const QString &callName, const QList<QVariant> &args);

private Q_SLOTS:
    void onPendingCallFinished(QDBusPendingCallWatcher *w);
    void onPropertyChanged(const QString &propName, const QVariant &value);

private:
    __AppManager1ApplicationPrivate *d_ptr;
};

#endif
