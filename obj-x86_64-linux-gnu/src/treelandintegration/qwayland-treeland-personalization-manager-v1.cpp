// This file was generated by qtwaylandscanner
// source file is treeland-personalization-manager-v1.xml

#include "qwayland-treeland-personalization-manager-v1.h"

QT_BEGIN_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_GCC("-Wmissing-field-initializers")
QT_WARNING_DISABLE_CLANG("-Wmissing-field-initializers")

namespace QtWayland {

static inline void *wlRegistryBind(struct ::wl_registry *registry, uint32_t name, const struct ::wl_interface *interface, uint32_t version)
{
    const uint32_t bindOpCode = 0;
    return (void *) wl_proxy_marshal_constructor_versioned((struct wl_proxy *) registry,
    bindOpCode, interface, version, name, interface->name, version, nullptr);
}

    treeland_personalization_manager_v1::treeland_personalization_manager_v1(struct ::wl_registry *registry, uint32_t id, int version)
    {
        init(registry, id, version);
    }

    treeland_personalization_manager_v1::treeland_personalization_manager_v1(struct ::treeland_personalization_manager_v1 *obj)
        : m_treeland_personalization_manager_v1(obj)
    {
    }

    treeland_personalization_manager_v1::treeland_personalization_manager_v1()
        : m_treeland_personalization_manager_v1(nullptr)
    {
    }

    treeland_personalization_manager_v1::~treeland_personalization_manager_v1()
    {
    }

    void treeland_personalization_manager_v1::init(struct ::wl_registry *registry, uint32_t id, int version)
    {
        m_treeland_personalization_manager_v1 = static_cast<struct ::treeland_personalization_manager_v1 *>(wlRegistryBind(registry, id, &treeland_personalization_manager_v1_interface, version));
    }

    void treeland_personalization_manager_v1::init(struct ::treeland_personalization_manager_v1 *obj)
    {
        m_treeland_personalization_manager_v1 = obj;
    }

    treeland_personalization_manager_v1 *treeland_personalization_manager_v1::fromObject(struct ::treeland_personalization_manager_v1 *object)
    {
        return static_cast<treeland_personalization_manager_v1 *>(treeland_personalization_manager_v1_get_user_data(object));
    }

    bool treeland_personalization_manager_v1::isInitialized() const
    {
        return m_treeland_personalization_manager_v1 != nullptr;
    }

    uint32_t treeland_personalization_manager_v1::version() const
    {
        return wl_proxy_get_version(reinterpret_cast<wl_proxy*>(m_treeland_personalization_manager_v1));
    }

    const struct wl_interface *treeland_personalization_manager_v1::interface()
    {
        return &::treeland_personalization_manager_v1_interface;
    }

    struct ::personalization_window_context_v1 *treeland_personalization_manager_v1::get_window_context(struct ::wl_surface *surface)
    {
        return ::treeland_personalization_manager_v1_get_window_context(
            m_treeland_personalization_manager_v1,
            surface);
    }

    struct ::personalization_wallpaper_context_v1 *treeland_personalization_manager_v1::get_wallpaper_context()
    {
        return ::treeland_personalization_manager_v1_get_wallpaper_context(
            m_treeland_personalization_manager_v1);
    }

    struct ::personalization_cursor_context_v1 *treeland_personalization_manager_v1::get_cursor_context()
    {
        return ::treeland_personalization_manager_v1_get_cursor_context(
            m_treeland_personalization_manager_v1);
    }

    personalization_wallpaper_context_v1::personalization_wallpaper_context_v1(struct ::wl_registry *registry, uint32_t id, int version)
    {
        init(registry, id, version);
    }

    personalization_wallpaper_context_v1::personalization_wallpaper_context_v1(struct ::personalization_wallpaper_context_v1 *obj)
        : m_personalization_wallpaper_context_v1(obj)
    {
        init_listener();
    }

    personalization_wallpaper_context_v1::personalization_wallpaper_context_v1()
        : m_personalization_wallpaper_context_v1(nullptr)
    {
    }

    personalization_wallpaper_context_v1::~personalization_wallpaper_context_v1()
    {
    }

    void personalization_wallpaper_context_v1::init(struct ::wl_registry *registry, uint32_t id, int version)
    {
        m_personalization_wallpaper_context_v1 = static_cast<struct ::personalization_wallpaper_context_v1 *>(wlRegistryBind(registry, id, &personalization_wallpaper_context_v1_interface, version));
        init_listener();
    }

    void personalization_wallpaper_context_v1::init(struct ::personalization_wallpaper_context_v1 *obj)
    {
        m_personalization_wallpaper_context_v1 = obj;
        init_listener();
    }

    personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1::fromObject(struct ::personalization_wallpaper_context_v1 *object)
    {
        if (wl_proxy_get_listener((struct ::wl_proxy *)object) != (void *)&m_personalization_wallpaper_context_v1_listener)
            return nullptr;
        return static_cast<personalization_wallpaper_context_v1 *>(personalization_wallpaper_context_v1_get_user_data(object));
    }

    bool personalization_wallpaper_context_v1::isInitialized() const
    {
        return m_personalization_wallpaper_context_v1 != nullptr;
    }

    uint32_t personalization_wallpaper_context_v1::version() const
    {
        return wl_proxy_get_version(reinterpret_cast<wl_proxy*>(m_personalization_wallpaper_context_v1));
    }

    const struct wl_interface *personalization_wallpaper_context_v1::interface()
    {
        return &::personalization_wallpaper_context_v1_interface;
    }

    void personalization_wallpaper_context_v1::set_fd(int32_t fd, const QString &metadata)
    {
        ::personalization_wallpaper_context_v1_set_fd(
            m_personalization_wallpaper_context_v1,
            fd,
            metadata.toUtf8().constData());
    }

    void personalization_wallpaper_context_v1::set_identifier(const QString &identifier)
    {
        ::personalization_wallpaper_context_v1_set_identifier(
            m_personalization_wallpaper_context_v1,
            identifier.toUtf8().constData());
    }

    void personalization_wallpaper_context_v1::set_output(const QString &output)
    {
        ::personalization_wallpaper_context_v1_set_output(
            m_personalization_wallpaper_context_v1,
            output.toUtf8().constData());
    }

    void personalization_wallpaper_context_v1::set_on(uint32_t options)
    {
        ::personalization_wallpaper_context_v1_set_on(
            m_personalization_wallpaper_context_v1,
            options);
    }

    void personalization_wallpaper_context_v1::set_isdark(uint32_t isdark)
    {
        ::personalization_wallpaper_context_v1_set_isdark(
            m_personalization_wallpaper_context_v1,
            isdark);
    }

    void personalization_wallpaper_context_v1::commit()
    {
        ::personalization_wallpaper_context_v1_commit(
            m_personalization_wallpaper_context_v1);
    }

    void personalization_wallpaper_context_v1::get_metadata()
    {
        ::personalization_wallpaper_context_v1_get_metadata(
            m_personalization_wallpaper_context_v1);
    }

    void personalization_wallpaper_context_v1::destroy()
    {
        ::personalization_wallpaper_context_v1_destroy(
            m_personalization_wallpaper_context_v1);
        m_personalization_wallpaper_context_v1 = nullptr;
    }

    void personalization_wallpaper_context_v1::personalization_wallpaper_context_v1_metadata(const QString &)
    {
    }

    void personalization_wallpaper_context_v1::handle_metadata(
        void *data,
        struct ::personalization_wallpaper_context_v1 *object,
        const char *metadata)
    {
        Q_UNUSED(object);
        static_cast<personalization_wallpaper_context_v1 *>(data)->personalization_wallpaper_context_v1_metadata(
            QString::fromUtf8(metadata));
    }

    const struct personalization_wallpaper_context_v1_listener personalization_wallpaper_context_v1::m_personalization_wallpaper_context_v1_listener = {
        personalization_wallpaper_context_v1::handle_metadata,
    };

    void personalization_wallpaper_context_v1::init_listener()
    {
        personalization_wallpaper_context_v1_add_listener(m_personalization_wallpaper_context_v1, &m_personalization_wallpaper_context_v1_listener, this);
    }

    personalization_cursor_context_v1::personalization_cursor_context_v1(struct ::wl_registry *registry, uint32_t id, int version)
    {
        init(registry, id, version);
    }

    personalization_cursor_context_v1::personalization_cursor_context_v1(struct ::personalization_cursor_context_v1 *obj)
        : m_personalization_cursor_context_v1(obj)
    {
        init_listener();
    }

    personalization_cursor_context_v1::personalization_cursor_context_v1()
        : m_personalization_cursor_context_v1(nullptr)
    {
    }

    personalization_cursor_context_v1::~personalization_cursor_context_v1()
    {
    }

    void personalization_cursor_context_v1::init(struct ::wl_registry *registry, uint32_t id, int version)
    {
        m_personalization_cursor_context_v1 = static_cast<struct ::personalization_cursor_context_v1 *>(wlRegistryBind(registry, id, &personalization_cursor_context_v1_interface, version));
        init_listener();
    }

    void personalization_cursor_context_v1::init(struct ::personalization_cursor_context_v1 *obj)
    {
        m_personalization_cursor_context_v1 = obj;
        init_listener();
    }

    personalization_cursor_context_v1 *personalization_cursor_context_v1::fromObject(struct ::personalization_cursor_context_v1 *object)
    {
        if (wl_proxy_get_listener((struct ::wl_proxy *)object) != (void *)&m_personalization_cursor_context_v1_listener)
            return nullptr;
        return static_cast<personalization_cursor_context_v1 *>(personalization_cursor_context_v1_get_user_data(object));
    }

    bool personalization_cursor_context_v1::isInitialized() const
    {
        return m_personalization_cursor_context_v1 != nullptr;
    }

    uint32_t personalization_cursor_context_v1::version() const
    {
        return wl_proxy_get_version(reinterpret_cast<wl_proxy*>(m_personalization_cursor_context_v1));
    }

    const struct wl_interface *personalization_cursor_context_v1::interface()
    {
        return &::personalization_cursor_context_v1_interface;
    }

    void personalization_cursor_context_v1::set_theme(const QString &name)
    {
        ::personalization_cursor_context_v1_set_theme(
            m_personalization_cursor_context_v1,
            name.toUtf8().constData());
    }

    void personalization_cursor_context_v1::get_theme()
    {
        ::personalization_cursor_context_v1_get_theme(
            m_personalization_cursor_context_v1);
    }

    void personalization_cursor_context_v1::set_size(uint32_t size)
    {
        ::personalization_cursor_context_v1_set_size(
            m_personalization_cursor_context_v1,
            size);
    }

    void personalization_cursor_context_v1::get_size()
    {
        ::personalization_cursor_context_v1_get_size(
            m_personalization_cursor_context_v1);
    }

    void personalization_cursor_context_v1::commit()
    {
        ::personalization_cursor_context_v1_commit(
            m_personalization_cursor_context_v1);
    }

    void personalization_cursor_context_v1::destroy()
    {
        ::personalization_cursor_context_v1_destroy(
            m_personalization_cursor_context_v1);
        m_personalization_cursor_context_v1 = nullptr;
    }

    void personalization_cursor_context_v1::personalization_cursor_context_v1_verfity(int32_t )
    {
    }

    void personalization_cursor_context_v1::handle_verfity(
        void *data,
        struct ::personalization_cursor_context_v1 *object,
        int32_t success)
    {
        Q_UNUSED(object);
        static_cast<personalization_cursor_context_v1 *>(data)->personalization_cursor_context_v1_verfity(
            success);
    }

    void personalization_cursor_context_v1::personalization_cursor_context_v1_theme(const QString &)
    {
    }

    void personalization_cursor_context_v1::handle_theme(
        void *data,
        struct ::personalization_cursor_context_v1 *object,
        const char *name)
    {
        Q_UNUSED(object);
        static_cast<personalization_cursor_context_v1 *>(data)->personalization_cursor_context_v1_theme(
            QString::fromUtf8(name));
    }

    void personalization_cursor_context_v1::personalization_cursor_context_v1_size(uint32_t )
    {
    }

    void personalization_cursor_context_v1::handle_size(
        void *data,
        struct ::personalization_cursor_context_v1 *object,
        uint32_t size)
    {
        Q_UNUSED(object);
        static_cast<personalization_cursor_context_v1 *>(data)->personalization_cursor_context_v1_size(
            size);
    }

    const struct personalization_cursor_context_v1_listener personalization_cursor_context_v1::m_personalization_cursor_context_v1_listener = {
        personalization_cursor_context_v1::handle_verfity,
        personalization_cursor_context_v1::handle_theme,
        personalization_cursor_context_v1::handle_size,
    };

    void personalization_cursor_context_v1::init_listener()
    {
        personalization_cursor_context_v1_add_listener(m_personalization_cursor_context_v1, &m_personalization_cursor_context_v1_listener, this);
    }

    personalization_window_context_v1::personalization_window_context_v1(struct ::wl_registry *registry, uint32_t id, int version)
    {
        init(registry, id, version);
    }

    personalization_window_context_v1::personalization_window_context_v1(struct ::personalization_window_context_v1 *obj)
        : m_personalization_window_context_v1(obj)
    {
    }

    personalization_window_context_v1::personalization_window_context_v1()
        : m_personalization_window_context_v1(nullptr)
    {
    }

    personalization_window_context_v1::~personalization_window_context_v1()
    {
    }

    void personalization_window_context_v1::init(struct ::wl_registry *registry, uint32_t id, int version)
    {
        m_personalization_window_context_v1 = static_cast<struct ::personalization_window_context_v1 *>(wlRegistryBind(registry, id, &personalization_window_context_v1_interface, version));
    }

    void personalization_window_context_v1::init(struct ::personalization_window_context_v1 *obj)
    {
        m_personalization_window_context_v1 = obj;
    }

    personalization_window_context_v1 *personalization_window_context_v1::fromObject(struct ::personalization_window_context_v1 *object)
    {
        return static_cast<personalization_window_context_v1 *>(personalization_window_context_v1_get_user_data(object));
    }

    bool personalization_window_context_v1::isInitialized() const
    {
        return m_personalization_window_context_v1 != nullptr;
    }

    uint32_t personalization_window_context_v1::version() const
    {
        return wl_proxy_get_version(reinterpret_cast<wl_proxy*>(m_personalization_window_context_v1));
    }

    const struct wl_interface *personalization_window_context_v1::interface()
    {
        return &::personalization_window_context_v1_interface;
    }

    void personalization_window_context_v1::set_background_type(uint32_t type)
    {
        ::personalization_window_context_v1_set_background_type(
            m_personalization_window_context_v1,
            type);
    }

    void personalization_window_context_v1::destroy()
    {
        ::personalization_window_context_v1_destroy(
            m_personalization_window_context_v1);
        m_personalization_window_context_v1 = nullptr;
    }
}

QT_WARNING_POP
QT_END_NAMESPACE
