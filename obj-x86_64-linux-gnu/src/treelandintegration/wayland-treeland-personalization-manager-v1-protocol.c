/* Generated by wayland-scanner 1.23.0 */

/*
 * SPDX-FileCopyrightText: 2023 UnionTech Software Technology Co., Ltd.
 * SPDX-License-Identifier: MIT
 *
 * Copyright © 2023 Uniontech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 */

#include <stdbool.h>
#include <stdlib.h>
#include <stdint.h>
#include "wayland-util.h"

extern const struct wl_interface personalization_cursor_context_v1_interface;
extern const struct wl_interface personalization_wallpaper_context_v1_interface;
extern const struct wl_interface personalization_window_context_v1_interface;
extern const struct wl_interface wl_surface_interface;

static const struct wl_interface *treeland_personalization_manager_v1_types[] = {
	NULL,
	NULL,
	&personalization_window_context_v1_interface,
	&wl_surface_interface,
	&personalization_wallpaper_context_v1_interface,
	&personalization_cursor_context_v1_interface,
};

static const struct wl_message treeland_personalization_manager_v1_requests[] = {
	{ "get_window_context", "no", treeland_personalization_manager_v1_types + 2 },
	{ "get_wallpaper_context", "n", treeland_personalization_manager_v1_types + 4 },
	{ "get_cursor_context", "n", treeland_personalization_manager_v1_types + 5 },
};

WL_EXPORT const struct wl_interface treeland_personalization_manager_v1_interface = {
	"treeland_personalization_manager_v1", 1,
	3, treeland_personalization_manager_v1_requests,
	0, NULL,
};

static const struct wl_message personalization_wallpaper_context_v1_requests[] = {
	{ "set_fd", "hs", treeland_personalization_manager_v1_types + 0 },
	{ "set_identifier", "s", treeland_personalization_manager_v1_types + 0 },
	{ "set_output", "s", treeland_personalization_manager_v1_types + 0 },
	{ "set_on", "u", treeland_personalization_manager_v1_types + 0 },
	{ "set_isdark", "u", treeland_personalization_manager_v1_types + 0 },
	{ "commit", "", treeland_personalization_manager_v1_types + 0 },
	{ "get_metadata", "", treeland_personalization_manager_v1_types + 0 },
	{ "destroy", "", treeland_personalization_manager_v1_types + 0 },
};

static const struct wl_message personalization_wallpaper_context_v1_events[] = {
	{ "metadata", "s", treeland_personalization_manager_v1_types + 0 },
};

WL_EXPORT const struct wl_interface personalization_wallpaper_context_v1_interface = {
	"personalization_wallpaper_context_v1", 1,
	8, personalization_wallpaper_context_v1_requests,
	1, personalization_wallpaper_context_v1_events,
};

static const struct wl_message personalization_cursor_context_v1_requests[] = {
	{ "set_theme", "s", treeland_personalization_manager_v1_types + 0 },
	{ "get_theme", "", treeland_personalization_manager_v1_types + 0 },
	{ "set_size", "u", treeland_personalization_manager_v1_types + 0 },
	{ "get_size", "", treeland_personalization_manager_v1_types + 0 },
	{ "commit", "", treeland_personalization_manager_v1_types + 0 },
	{ "destroy", "", treeland_personalization_manager_v1_types + 0 },
};

static const struct wl_message personalization_cursor_context_v1_events[] = {
	{ "verfity", "i", treeland_personalization_manager_v1_types + 0 },
	{ "theme", "s", treeland_personalization_manager_v1_types + 0 },
	{ "size", "u", treeland_personalization_manager_v1_types + 0 },
};

WL_EXPORT const struct wl_interface personalization_cursor_context_v1_interface = {
	"personalization_cursor_context_v1", 1,
	6, personalization_cursor_context_v1_requests,
	3, personalization_cursor_context_v1_events,
};

static const struct wl_message personalization_window_context_v1_requests[] = {
	{ "set_background_type", "u", treeland_personalization_manager_v1_types + 0 },
	{ "destroy", "", treeland_personalization_manager_v1_types + 0 },
};

WL_EXPORT const struct wl_interface personalization_window_context_v1_interface = {
	"personalization_window_context_v1", 1,
	2, personalization_window_context_v1_requests,
	0, NULL,
};

