/* Generated by wayland-scanner 1.23.0 */

#ifndef TREELAND_PERSONALIZATION_MANAGER_V1_CLIENT_PROTOCOL_H
#define TREELAND_PERSONALIZATION_MANAGER_V1_CLIENT_PROTOCOL_H

#include <stdint.h>
#include <stddef.h>
#include "wayland-client-core.h"

#ifdef  __cplusplus
extern "C" {
#endif

/**
 * @page page_treeland_personalization_manager_v1 The treeland_personalization_manager_v1 protocol
 * @section page_ifaces_treeland_personalization_manager_v1 Interfaces
 * - @subpage page_iface_treeland_personalization_manager_v1 - personalization manager
 * - @subpage page_iface_personalization_wallpaper_context_v1 - client custom wallpaper context
 * - @subpage page_iface_personalization_cursor_context_v1 - client custom cursor context
 * - @subpage page_iface_personalization_window_context_v1 - client custom window context
 * @section page_copyright_treeland_personalization_manager_v1 Copyright
 * <pre>
 *
 * SPDX-FileCopyrightText: 2023 UnionTech Software Technology Co., Ltd.
 * SPDX-License-Identifier: MIT
 *
 * Copyright © 2023 Uniontech
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice (including the next
 * paragraph) shall be included in all copies or substantial portions of the
 * Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 * </pre>
 */
struct personalization_cursor_context_v1;
struct personalization_wallpaper_context_v1;
struct personalization_window_context_v1;
struct treeland_personalization_manager_v1;
struct wl_surface;

#ifndef TREELAND_PERSONALIZATION_MANAGER_V1_INTERFACE
#define TREELAND_PERSONALIZATION_MANAGER_V1_INTERFACE
/**
 * @page page_iface_treeland_personalization_manager_v1 treeland_personalization_manager_v1
 * @section page_iface_treeland_personalization_manager_v1_desc Description
 *
 * This interface allows a client to customized display effects.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 * @section page_iface_treeland_personalization_manager_v1_api API
 * See @ref iface_treeland_personalization_manager_v1.
 */
/**
 * @defgroup iface_treeland_personalization_manager_v1 The treeland_personalization_manager_v1 interface
 *
 * This interface allows a client to customized display effects.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 */
extern const struct wl_interface treeland_personalization_manager_v1_interface;
#endif
#ifndef PERSONALIZATION_WALLPAPER_CONTEXT_V1_INTERFACE
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_INTERFACE
/**
 * @page page_iface_personalization_wallpaper_context_v1 personalization_wallpaper_context_v1
 * @section page_iface_personalization_wallpaper_context_v1_desc Description
 *
 * This interface allows a client personalization wallpaper.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 * @section page_iface_personalization_wallpaper_context_v1_api API
 * See @ref iface_personalization_wallpaper_context_v1.
 */
/**
 * @defgroup iface_personalization_wallpaper_context_v1 The personalization_wallpaper_context_v1 interface
 *
 * This interface allows a client personalization wallpaper.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 */
extern const struct wl_interface personalization_wallpaper_context_v1_interface;
#endif
#ifndef PERSONALIZATION_CURSOR_CONTEXT_V1_INTERFACE
#define PERSONALIZATION_CURSOR_CONTEXT_V1_INTERFACE
/**
 * @page page_iface_personalization_cursor_context_v1 personalization_cursor_context_v1
 * @section page_iface_personalization_cursor_context_v1_desc Description
 *
 * This interface allows a client personalization cursor.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 * @section page_iface_personalization_cursor_context_v1_api API
 * See @ref iface_personalization_cursor_context_v1.
 */
/**
 * @defgroup iface_personalization_cursor_context_v1 The personalization_cursor_context_v1 interface
 *
 * This interface allows a client personalization cursor.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 */
extern const struct wl_interface personalization_cursor_context_v1_interface;
#endif
#ifndef PERSONALIZATION_WINDOW_CONTEXT_V1_INTERFACE
#define PERSONALIZATION_WINDOW_CONTEXT_V1_INTERFACE
/**
 * @page page_iface_personalization_window_context_v1 personalization_window_context_v1
 * @section page_iface_personalization_window_context_v1_desc Description
 *
 * This interface allows a client personalization window.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 * @section page_iface_personalization_window_context_v1_api API
 * See @ref iface_personalization_window_context_v1.
 */
/**
 * @defgroup iface_personalization_window_context_v1 The personalization_window_context_v1 interface
 *
 * This interface allows a client personalization window.
 *
 * Warning! The protocol described in this file is currently in the testing
 * phase. Backward compatible changes may be added together with the
 * corresponding interface version bump. Backward incompatible changes can
 * only be done by creating a new major version of the extension.
 */
extern const struct wl_interface personalization_window_context_v1_interface;
#endif

#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_WINDOW_CONTEXT 0
#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_WALLPAPER_CONTEXT 1
#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_CURSOR_CONTEXT 2


/**
 * @ingroup iface_treeland_personalization_manager_v1
 */
#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_WINDOW_CONTEXT_SINCE_VERSION 1
/**
 * @ingroup iface_treeland_personalization_manager_v1
 */
#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_WALLPAPER_CONTEXT_SINCE_VERSION 1
/**
 * @ingroup iface_treeland_personalization_manager_v1
 */
#define TREELAND_PERSONALIZATION_MANAGER_V1_GET_CURSOR_CONTEXT_SINCE_VERSION 1

/** @ingroup iface_treeland_personalization_manager_v1 */
static inline void
treeland_personalization_manager_v1_set_user_data(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) treeland_personalization_manager_v1, user_data);
}

/** @ingroup iface_treeland_personalization_manager_v1 */
static inline void *
treeland_personalization_manager_v1_get_user_data(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1)
{
	return wl_proxy_get_user_data((struct wl_proxy *) treeland_personalization_manager_v1);
}

static inline uint32_t
treeland_personalization_manager_v1_get_version(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1)
{
	return wl_proxy_get_version((struct wl_proxy *) treeland_personalization_manager_v1);
}

/** @ingroup iface_treeland_personalization_manager_v1 */
static inline void
treeland_personalization_manager_v1_destroy(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1)
{
	wl_proxy_destroy((struct wl_proxy *) treeland_personalization_manager_v1);
}

/**
 * @ingroup iface_treeland_personalization_manager_v1
 *
 * set window background, shadow based on context
 */
static inline struct personalization_window_context_v1 *
treeland_personalization_manager_v1_get_window_context(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1, struct wl_surface *surface)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_flags((struct wl_proxy *) treeland_personalization_manager_v1,
			 TREELAND_PERSONALIZATION_MANAGER_V1_GET_WINDOW_CONTEXT, &personalization_window_context_v1_interface, wl_proxy_get_version((struct wl_proxy *) treeland_personalization_manager_v1), 0, NULL, surface);

	return (struct personalization_window_context_v1 *) id;
}

/**
 * @ingroup iface_treeland_personalization_manager_v1
 *
 * custom user wallpaper
 */
static inline struct personalization_wallpaper_context_v1 *
treeland_personalization_manager_v1_get_wallpaper_context(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_flags((struct wl_proxy *) treeland_personalization_manager_v1,
			 TREELAND_PERSONALIZATION_MANAGER_V1_GET_WALLPAPER_CONTEXT, &personalization_wallpaper_context_v1_interface, wl_proxy_get_version((struct wl_proxy *) treeland_personalization_manager_v1), 0, NULL);

	return (struct personalization_wallpaper_context_v1 *) id;
}

/**
 * @ingroup iface_treeland_personalization_manager_v1
 *
 * custom user cursor
 */
static inline struct personalization_cursor_context_v1 *
treeland_personalization_manager_v1_get_cursor_context(struct treeland_personalization_manager_v1 *treeland_personalization_manager_v1)
{
	struct wl_proxy *id;

	id = wl_proxy_marshal_flags((struct wl_proxy *) treeland_personalization_manager_v1,
			 TREELAND_PERSONALIZATION_MANAGER_V1_GET_CURSOR_CONTEXT, &personalization_cursor_context_v1_interface, wl_proxy_get_version((struct wl_proxy *) treeland_personalization_manager_v1), 0, NULL);

	return (struct personalization_cursor_context_v1 *) id;
}

#ifndef PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_ENUM
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_ENUM
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 * xdg desktop portal supported keys
 */
enum personalization_wallpaper_context_v1_options {
	/**
	 * whether to show a preview of the picture
	 */
	PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_PREVIEW = 1,
	/**
	 * configure screen background
	 */
	PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_BACKGROUND = 2,
	/**
	 * configure screen wallpaper
	 */
	PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_LOCKSCREEN = 4,
};
#endif /* PERSONALIZATION_WALLPAPER_CONTEXT_V1_OPTIONS_ENUM */

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 * @struct personalization_wallpaper_context_v1_listener
 */
struct personalization_wallpaper_context_v1_listener {
	/**
	 * get metadata event
	 *
	 * Send this signal after getting the user's wallpaper.
	 * @param metadata user meta data
	 */
	void (*metadata)(void *data,
			 struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1,
			 const char *metadata);
};

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline int
personalization_wallpaper_context_v1_add_listener(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1,
						  const struct personalization_wallpaper_context_v1_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) personalization_wallpaper_context_v1,
				     (void (**)(void)) listener, data);
}

#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_FD 0
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_IDENTIFIER 1
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_OUTPUT 2
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ON 3
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ISDARK 4
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_COMMIT 5
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_GET_METADATA 6
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_DESTROY 7

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_METADATA_SINCE_VERSION 1

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_FD_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_IDENTIFIER_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_OUTPUT_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ON_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ISDARK_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_COMMIT_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_GET_METADATA_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
#define PERSONALIZATION_WALLPAPER_CONTEXT_V1_DESTROY_SINCE_VERSION 1

/** @ingroup iface_personalization_wallpaper_context_v1 */
static inline void
personalization_wallpaper_context_v1_set_user_data(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) personalization_wallpaper_context_v1, user_data);
}

/** @ingroup iface_personalization_wallpaper_context_v1 */
static inline void *
personalization_wallpaper_context_v1_get_user_data(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1)
{
	return wl_proxy_get_user_data((struct wl_proxy *) personalization_wallpaper_context_v1);
}

static inline uint32_t
personalization_wallpaper_context_v1_get_version(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1)
{
	return wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_set_fd(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, int32_t fd, const char *metadata)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_FD, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0, fd, metadata);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_set_identifier(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, const char *identifier)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_IDENTIFIER, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0, identifier);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_set_output(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, const char *output)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_OUTPUT, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0, output);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_set_on(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, uint32_t options)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ON, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0, options);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_set_isdark(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1, uint32_t isdark)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_SET_ISDARK, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0, isdark);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 */
static inline void
personalization_wallpaper_context_v1_commit(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_COMMIT, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 *
 * get the current user's wallpaper
 */
static inline void
personalization_wallpaper_context_v1_get_metadata(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_GET_METADATA, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), 0);
}

/**
 * @ingroup iface_personalization_wallpaper_context_v1
 *
 * Destroy the context object.
 */
static inline void
personalization_wallpaper_context_v1_destroy(struct personalization_wallpaper_context_v1 *personalization_wallpaper_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_wallpaper_context_v1,
			 PERSONALIZATION_WALLPAPER_CONTEXT_V1_DESTROY, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_wallpaper_context_v1), WL_MARSHAL_FLAG_DESTROY);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 * @struct personalization_cursor_context_v1_listener
 */
struct personalization_cursor_context_v1_listener {
	/**
	 * verfity event
	 *
	 * Send this signal after commit cursor configure.
	 * @param success check whether the configuration is successful
	 */
	void (*verfity)(void *data,
			struct personalization_cursor_context_v1 *personalization_cursor_context_v1,
			int32_t success);
	/**
	 * cursor theme changed event
	 *
	 * Send this signal after system cursor theme changed.
	 * @param name cursor theme name
	 */
	void (*theme)(void *data,
		      struct personalization_cursor_context_v1 *personalization_cursor_context_v1,
		      const char *name);
	/**
	 * cursor size changed event
	 *
	 * Send this signal after system cursor size changed.
	 * @param size cursor size
	 */
	void (*size)(void *data,
		     struct personalization_cursor_context_v1 *personalization_cursor_context_v1,
		     uint32_t size);
};

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
static inline int
personalization_cursor_context_v1_add_listener(struct personalization_cursor_context_v1 *personalization_cursor_context_v1,
					       const struct personalization_cursor_context_v1_listener *listener, void *data)
{
	return wl_proxy_add_listener((struct wl_proxy *) personalization_cursor_context_v1,
				     (void (**)(void)) listener, data);
}

#define PERSONALIZATION_CURSOR_CONTEXT_V1_SET_THEME 0
#define PERSONALIZATION_CURSOR_CONTEXT_V1_GET_THEME 1
#define PERSONALIZATION_CURSOR_CONTEXT_V1_SET_SIZE 2
#define PERSONALIZATION_CURSOR_CONTEXT_V1_GET_SIZE 3
#define PERSONALIZATION_CURSOR_CONTEXT_V1_COMMIT 4
#define PERSONALIZATION_CURSOR_CONTEXT_V1_DESTROY 5

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_VERFITY_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_THEME_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_SIZE_SINCE_VERSION 1

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_SET_THEME_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_GET_THEME_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_SET_SIZE_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_GET_SIZE_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_COMMIT_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_cursor_context_v1
 */
#define PERSONALIZATION_CURSOR_CONTEXT_V1_DESTROY_SINCE_VERSION 1

/** @ingroup iface_personalization_cursor_context_v1 */
static inline void
personalization_cursor_context_v1_set_user_data(struct personalization_cursor_context_v1 *personalization_cursor_context_v1, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) personalization_cursor_context_v1, user_data);
}

/** @ingroup iface_personalization_cursor_context_v1 */
static inline void *
personalization_cursor_context_v1_get_user_data(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	return wl_proxy_get_user_data((struct wl_proxy *) personalization_cursor_context_v1);
}

static inline uint32_t
personalization_cursor_context_v1_get_version(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	return wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
static inline void
personalization_cursor_context_v1_set_theme(struct personalization_cursor_context_v1 *personalization_cursor_context_v1, const char *name)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_SET_THEME, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), 0, name);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
static inline void
personalization_cursor_context_v1_get_theme(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_GET_THEME, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), 0);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
static inline void
personalization_cursor_context_v1_set_size(struct personalization_cursor_context_v1 *personalization_cursor_context_v1, uint32_t size)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_SET_SIZE, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), 0, size);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 */
static inline void
personalization_cursor_context_v1_get_size(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_GET_SIZE, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), 0);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 *
 * if only one commit fails validation, the commit will fail
 */
static inline void
personalization_cursor_context_v1_commit(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_COMMIT, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), 0);
}

/**
 * @ingroup iface_personalization_cursor_context_v1
 *
 * Destroy the context object.
 */
static inline void
personalization_cursor_context_v1_destroy(struct personalization_cursor_context_v1 *personalization_cursor_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_cursor_context_v1,
			 PERSONALIZATION_CURSOR_CONTEXT_V1_DESTROY, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_cursor_context_v1), WL_MARSHAL_FLAG_DESTROY);
}

#ifndef PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_ENUM
#define PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_ENUM
/**
 * @ingroup iface_personalization_window_context_v1
 * Windows will have different background effects
 *
 * This will instruct the compositor how to set the background
 * for the window, desktop.
 */
enum personalization_window_context_v1_background_type {
	/**
	 * not draw the background image
	 */
	PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_NORMAL = 0,
	/**
	 * draw the background image
	 */
	PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_WALLPAPER = 1,
	/**
	 * draw the blend background image
	 */
	PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_BLEND = 2,
};
#endif /* PERSONALIZATION_WINDOW_CONTEXT_V1_BACKGROUND_TYPE_ENUM */

#define PERSONALIZATION_WINDOW_CONTEXT_V1_SET_BACKGROUND_TYPE 0
#define PERSONALIZATION_WINDOW_CONTEXT_V1_DESTROY 1


/**
 * @ingroup iface_personalization_window_context_v1
 */
#define PERSONALIZATION_WINDOW_CONTEXT_V1_SET_BACKGROUND_TYPE_SINCE_VERSION 1
/**
 * @ingroup iface_personalization_window_context_v1
 */
#define PERSONALIZATION_WINDOW_CONTEXT_V1_DESTROY_SINCE_VERSION 1

/** @ingroup iface_personalization_window_context_v1 */
static inline void
personalization_window_context_v1_set_user_data(struct personalization_window_context_v1 *personalization_window_context_v1, void *user_data)
{
	wl_proxy_set_user_data((struct wl_proxy *) personalization_window_context_v1, user_data);
}

/** @ingroup iface_personalization_window_context_v1 */
static inline void *
personalization_window_context_v1_get_user_data(struct personalization_window_context_v1 *personalization_window_context_v1)
{
	return wl_proxy_get_user_data((struct wl_proxy *) personalization_window_context_v1);
}

static inline uint32_t
personalization_window_context_v1_get_version(struct personalization_window_context_v1 *personalization_window_context_v1)
{
	return wl_proxy_get_version((struct wl_proxy *) personalization_window_context_v1);
}

/**
 * @ingroup iface_personalization_window_context_v1
 *
 * Destroy the context object.
 */
static inline void
personalization_window_context_v1_set_background_type(struct personalization_window_context_v1 *personalization_window_context_v1, uint32_t type)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_window_context_v1,
			 PERSONALIZATION_WINDOW_CONTEXT_V1_SET_BACKGROUND_TYPE, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_window_context_v1), 0, type);
}

/**
 * @ingroup iface_personalization_window_context_v1
 *
 * Destroy the context object.
 */
static inline void
personalization_window_context_v1_destroy(struct personalization_window_context_v1 *personalization_window_context_v1)
{
	wl_proxy_marshal_flags((struct wl_proxy *) personalization_window_context_v1,
			 PERSONALIZATION_WINDOW_CONTEXT_V1_DESTROY, NULL, wl_proxy_get_version((struct wl_proxy *) personalization_window_context_v1), WL_MARSHAL_FLAG_DESTROY);
}

#ifdef  __cplusplus
}
#endif

#endif
