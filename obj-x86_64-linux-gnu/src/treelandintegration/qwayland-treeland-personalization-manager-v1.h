// This file was generated by qtwaylandscanner
// source file is treeland-personalization-manager-v1.xml

#ifndef QT_WAYLAND_TREELAND_PERSONALIZATION_MANAGER_V1
#define QT_WAYLAND_TREELAND_PERSONALIZATION_MANAGER_V1

#include "wayland-treeland-personalization-manager-v1-client-protocol.h"
#include <QByteArray>
#include <QString>

struct wl_registry;

QT_BEGIN_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_GCC("-Wmissing-field-initializers")
QT_WARNING_DISABLE_CLANG("-Wmissing-field-initializers")

namespace QtWayland {
    class  treeland_personalization_manager_v1
    {
    public:
        treeland_personalization_manager_v1(struct ::wl_registry *registry, uint32_t id, int version);
        treeland_personalization_manager_v1(struct ::treeland_personalization_manager_v1 *object);
        treeland_personalization_manager_v1();

        virtual ~treeland_personalization_manager_v1();

        void init(struct ::wl_registry *registry, uint32_t id, int version);
        void init(struct ::treeland_personalization_manager_v1 *object);

        struct ::treeland_personalization_manager_v1 *object() { return m_treeland_personalization_manager_v1; }
        const struct ::treeland_personalization_manager_v1 *object() const { return m_treeland_personalization_manager_v1; }
        static treeland_personalization_manager_v1 *fromObject(struct ::treeland_personalization_manager_v1 *object);

        bool isInitialized() const;

        uint32_t version() const;
        static const struct ::wl_interface *interface();

        struct ::personalization_window_context_v1 *get_window_context(struct ::wl_surface *surface);
        struct ::personalization_wallpaper_context_v1 *get_wallpaper_context();
        struct ::personalization_cursor_context_v1 *get_cursor_context();

    private:
        struct ::treeland_personalization_manager_v1 *m_treeland_personalization_manager_v1;
    };

    class  personalization_wallpaper_context_v1
    {
    public:
        personalization_wallpaper_context_v1(struct ::wl_registry *registry, uint32_t id, int version);
        personalization_wallpaper_context_v1(struct ::personalization_wallpaper_context_v1 *object);
        personalization_wallpaper_context_v1();

        virtual ~personalization_wallpaper_context_v1();

        void init(struct ::wl_registry *registry, uint32_t id, int version);
        void init(struct ::personalization_wallpaper_context_v1 *object);

        struct ::personalization_wallpaper_context_v1 *object() { return m_personalization_wallpaper_context_v1; }
        const struct ::personalization_wallpaper_context_v1 *object() const { return m_personalization_wallpaper_context_v1; }
        static personalization_wallpaper_context_v1 *fromObject(struct ::personalization_wallpaper_context_v1 *object);

        bool isInitialized() const;

        uint32_t version() const;
        static const struct ::wl_interface *interface();

        enum options {
            options_preview = 1, // whether to show a preview of the picture
            options_background = 2, // configure screen background
            options_lockscreen = 4, // configure screen wallpaper
        };

        void set_fd(int32_t fd, const QString &metadata);
        void set_identifier(const QString &identifier);
        void set_output(const QString &output);
        void set_on(uint32_t options);
        void set_isdark(uint32_t isdark);
        void commit();
        void get_metadata();
        void destroy();

    protected:
        virtual void personalization_wallpaper_context_v1_metadata(const QString &metadata);

    private:
        void init_listener();
        static const struct personalization_wallpaper_context_v1_listener m_personalization_wallpaper_context_v1_listener;
        static void handle_metadata(
            void *data,
            struct ::personalization_wallpaper_context_v1 *object,
            const char *metadata);
        struct ::personalization_wallpaper_context_v1 *m_personalization_wallpaper_context_v1;
    };

    class  personalization_cursor_context_v1
    {
    public:
        personalization_cursor_context_v1(struct ::wl_registry *registry, uint32_t id, int version);
        personalization_cursor_context_v1(struct ::personalization_cursor_context_v1 *object);
        personalization_cursor_context_v1();

        virtual ~personalization_cursor_context_v1();

        void init(struct ::wl_registry *registry, uint32_t id, int version);
        void init(struct ::personalization_cursor_context_v1 *object);

        struct ::personalization_cursor_context_v1 *object() { return m_personalization_cursor_context_v1; }
        const struct ::personalization_cursor_context_v1 *object() const { return m_personalization_cursor_context_v1; }
        static personalization_cursor_context_v1 *fromObject(struct ::personalization_cursor_context_v1 *object);

        bool isInitialized() const;

        uint32_t version() const;
        static const struct ::wl_interface *interface();

        void set_theme(const QString &name);
        void get_theme();
        void set_size(uint32_t size);
        void get_size();
        void commit();
        void destroy();

    protected:
        virtual void personalization_cursor_context_v1_verfity(int32_t success);
        virtual void personalization_cursor_context_v1_theme(const QString &name);
        virtual void personalization_cursor_context_v1_size(uint32_t size);

    private:
        void init_listener();
        static const struct personalization_cursor_context_v1_listener m_personalization_cursor_context_v1_listener;
        static void handle_verfity(
            void *data,
            struct ::personalization_cursor_context_v1 *object,
            int32_t success);
        static void handle_theme(
            void *data,
            struct ::personalization_cursor_context_v1 *object,
            const char *name);
        static void handle_size(
            void *data,
            struct ::personalization_cursor_context_v1 *object,
            uint32_t size);
        struct ::personalization_cursor_context_v1 *m_personalization_cursor_context_v1;
    };

    class  personalization_window_context_v1
    {
    public:
        personalization_window_context_v1(struct ::wl_registry *registry, uint32_t id, int version);
        personalization_window_context_v1(struct ::personalization_window_context_v1 *object);
        personalization_window_context_v1();

        virtual ~personalization_window_context_v1();

        void init(struct ::wl_registry *registry, uint32_t id, int version);
        void init(struct ::personalization_window_context_v1 *object);

        struct ::personalization_window_context_v1 *object() { return m_personalization_window_context_v1; }
        const struct ::personalization_window_context_v1 *object() const { return m_personalization_window_context_v1; }
        static personalization_window_context_v1 *fromObject(struct ::personalization_window_context_v1 *object);

        bool isInitialized() const;

        uint32_t version() const;
        static const struct ::wl_interface *interface();

        enum background_type {
            background_type_normal = 0, // not draw the background image
            background_type_wallpaper = 1, // draw the background image
            background_type_blend = 2, // draw the blend background image
        };

        void set_background_type(uint32_t type);
        void destroy();

    private:
        struct ::personalization_window_context_v1 *m_personalization_window_context_v1;
    };
}

QT_WARNING_POP
QT_END_NAMESPACE

#endif
