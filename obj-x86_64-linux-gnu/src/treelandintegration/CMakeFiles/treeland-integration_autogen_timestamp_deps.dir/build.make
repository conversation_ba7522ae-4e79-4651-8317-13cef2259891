# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Utility rule file for treeland-integration_autogen_timestamp_deps.

# Include any custom commands dependencies for this target.
include src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/compiler_depend.make

# Include the progress variables for this target.
include src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/progress.make

src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps: src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp
src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps: src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps: src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps: src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c

src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp: /usr/lib/qt6/libexec/qtwaylandscanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating qwayland-treeland-personalization-manager-v1.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/lib/qt6/libexec/qtwaylandscanner client-code /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml --build-macro=QT_BUILD_TREELAND_INTEGRATION_LIB --header-path='' --add-include='' > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp

src/treelandintegration/qwayland-treeland-personalization-manager-v1.h: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/qwayland-treeland-personalization-manager-v1.h: /usr/lib/qt6/libexec/qtwaylandscanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating qwayland-treeland-personalization-manager-v1.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/lib/qt6/libexec/qtwaylandscanner client-header /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml --build-macro=QT_BUILD_TREELAND_INTEGRATION_LIB --header-path="" > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h

src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h: /usr/bin/wayland-scanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Generating wayland-treeland-personalization-manager-v1-client-protocol.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/wayland-scanner --include-core-only client-header < /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h

src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c: /usr/bin/wayland-scanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating wayland-treeland-personalization-manager-v1-protocol.c"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/wayland-scanner --include-core-only public-code < /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c

src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/codegen:
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/codegen

treeland-integration_autogen_timestamp_deps: src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps
treeland-integration_autogen_timestamp_deps: src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp
treeland-integration_autogen_timestamp_deps: src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
treeland-integration_autogen_timestamp_deps: src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
treeland-integration_autogen_timestamp_deps: src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c
treeland-integration_autogen_timestamp_deps: src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/build.make
.PHONY : treeland-integration_autogen_timestamp_deps

# Rule to build all files generated by this target.
src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/build: treeland-integration_autogen_timestamp_deps
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/build

src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && $(CMAKE_COMMAND) -P CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/cmake_clean.cmake
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/clean

src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/depend:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration_autogen_timestamp_deps.dir/depend

