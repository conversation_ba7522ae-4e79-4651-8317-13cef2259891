# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_WAYLANDCLIENT_LIB -DQT_WAYLANDGLOBAL_LIB

C_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/include -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0/QtWaylandGlobal

C_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -fPIC -fPIC

CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_WAYLANDCLIENT_LIB -DQT_WAYLANDGLOBAL_LIB

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/include -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0 -isystem /usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0/QtWaylandGlobal

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC

