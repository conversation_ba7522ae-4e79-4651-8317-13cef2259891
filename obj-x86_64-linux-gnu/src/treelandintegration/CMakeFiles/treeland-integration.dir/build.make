# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/treelandintegration/CMakeFiles/treeland-integration.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.make

# Include the progress variables for this target.
include src/treelandintegration/CMakeFiles/treeland-integration.dir/progress.make

# Include the compile flags for this target's objects.
include src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make

src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h: /usr/bin/wayland-scanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating wayland-treeland-personalization-manager-v1-client-protocol.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/wayland-scanner --include-core-only client-header < /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h

src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c: /usr/bin/wayland-scanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating wayland-treeland-personalization-manager-v1-protocol.c"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/wayland-scanner --include-core-only public-code < /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c

src/treelandintegration/qwayland-treeland-personalization-manager-v1.h: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/qwayland-treeland-personalization-manager-v1.h: /usr/lib/qt6/libexec/qtwaylandscanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Generating qwayland-treeland-personalization-manager-v1.h"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/lib/qt6/libexec/qtwaylandscanner client-header /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml --build-macro=QT_BUILD_TREELAND_INTEGRATION_LIB --header-path="" > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h

src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml
src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp: /usr/lib/qt6/libexec/qtwaylandscanner
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Generating qwayland-treeland-personalization-manager-v1.cpp"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/lib/qt6/libexec/qtwaylandscanner client-code /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/treeland-personalization-manager-v1.xml --build-macro=QT_BUILD_TREELAND_INTEGRATION_LIB --header-path='' --add-include='' > /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp

src/treelandintegration/treeland-integration_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/treelandintegration/treeland-integration_autogen/timestamp: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Automatic MOC for target treeland-integration"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/CMakeFiles/treeland-integration_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/timestamp

src/treelandintegration/CMakeFiles/treeland-integration.dir/codegen:
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration.dir/codegen

src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make
src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o: src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp
src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o -MF CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp

src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp > CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.i

src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp -o CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.s

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make
src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp
src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o -MF CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o.d -o CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp > CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.i

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp -o CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.s

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make
src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp
src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o -MF CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o.d -o CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp > CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.i

src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp -o CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.s

src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make
src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o: src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c
src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o -MF CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o.d -o CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c

src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c > CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.i

src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c -o CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.s

src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/flags.make
src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o: src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp
src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o: src/treelandintegration/CMakeFiles/treeland-integration.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o -MF CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o.d -o CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp

src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp > CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.i

src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp -o CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.s

treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o
treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o
treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o
treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o
treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o
treeland-integration: src/treelandintegration/CMakeFiles/treeland-integration.dir/build.make
.PHONY : treeland-integration

# Rule to build all files generated by this target.
src/treelandintegration/CMakeFiles/treeland-integration.dir/build: treeland-integration
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration.dir/build

src/treelandintegration/CMakeFiles/treeland-integration.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration && $(CMAKE_COMMAND) -P CMakeFiles/treeland-integration.dir/cmake_clean.cmake
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration.dir/clean

src/treelandintegration/CMakeFiles/treeland-integration.dir/depend: src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp
src/treelandintegration/CMakeFiles/treeland-integration.dir/depend: src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
src/treelandintegration/CMakeFiles/treeland-integration.dir/depend: src/treelandintegration/treeland-integration_autogen/timestamp
src/treelandintegration/CMakeFiles/treeland-integration.dir/depend: src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
src/treelandintegration/CMakeFiles/treeland-integration.dir/depend: src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/CMakeFiles/treeland-integration.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/treelandintegration/CMakeFiles/treeland-integration.dir/depend

