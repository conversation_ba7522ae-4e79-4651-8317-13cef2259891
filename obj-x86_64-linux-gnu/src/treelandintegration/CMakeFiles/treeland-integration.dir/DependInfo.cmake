
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c" "src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o" "gcc" "src/treelandintegration/CMakeFiles/treeland-integration.dir/wayland-treeland-personalization-manager-v1-protocol.c.o.d"
  "" "src/treelandintegration/treeland-integration_autogen/timestamp" "custom" "src/treelandintegration/treeland-integration_autogen/deps"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp" "src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o" "gcc" "src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationmanager.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp" "src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o" "gcc" "src/treelandintegration/CMakeFiles/treeland-integration.dir/personalizationwindow.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp" "src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o" "gcc" "src/treelandintegration/CMakeFiles/treeland-integration.dir/qwayland-treeland-personalization-manager-v1.cpp.o.d"
  "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp" "src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o" "gcc" "src/treelandintegration/CMakeFiles/treeland-integration.dir/treeland-integration_autogen/mocs_compilation.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
