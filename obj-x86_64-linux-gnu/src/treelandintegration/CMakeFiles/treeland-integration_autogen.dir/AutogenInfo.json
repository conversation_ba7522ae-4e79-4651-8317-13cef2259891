{"BUILD_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6ConfigExtras.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtInstallPaths.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Targets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtFeatureCommon.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicAppleHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicGitHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicPluginHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicSbomHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTargetHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicTestHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicToolHelpers.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/Qt6Dependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules/FindWayland.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules/ECMFindModuleHelpersStub.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/modules/ECMFindModuleHelpers.cmake", "/usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.31/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.31/Modules/FindPkgConfig.cmake", "/usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.31/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.31/Modules/FeatureSummary.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsDependencies.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules/FindWaylandScanner.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules/ECMFindModuleHelpersStub.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6/3rdparty/extra-cmake-modules/modules/ECMFindModuleHelpers.cmake", "/usr/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.31/Modules/FindPackageMessage.cmake", "/usr/share/cmake-3.31/Modules/FeatureSummary.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandScannerTools/Qt6WaylandScannerToolsVersionlessTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateConfigVersionImpl.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateConfig.cmake", "/usr/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandGlobalPrivate/Qt6WaylandGlobalPrivateVersionlessAliasTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientTargets.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientTargets-none.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientAdditionalTargetInfo.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientMacros.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientPlugins.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt6WaylandClient/Qt6WaylandClientVersionlessAliasTargets.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad", "CROSS_CONFIG": false, "DEP_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/deps", "DEP_FILE_RULE_NAME": "treeland-integration_autogen/timestamp", "HEADERS": [["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h", "Mu", "EJRQKI7XPS/moc_qwayland-treeland-personalization-manager-v1.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h", "Mu", "EJRQKI7XPS/moc_wayland-treeland-personalization-manager-v1-client-protocol.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.h", "Mu", "EWIEGA46WW/moc_personalizationmanager.cpp", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.h", "Mu", "EWIEGA46WW/moc_personalizationwindow.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["DSG_DATA_DIR=\"/usr/share/dsg\"", "QT_CORE_LIB", "QT_GUI_LIB", "QT_MESSAGELOGCONTEXT", "QT_NO_DEBUG", "QT_WAYLANDCLIENT_LIB", "QT_WAYLANDGLOBAL_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration", "/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient", "/usr/include/x86_64-linux-gnu/qt6", "/usr/include/x86_64-linux-gnu/qt6/QtCore", "/usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++", "/usr/include/x86_64-linux-gnu/qt6/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore", "/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0", "/usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0/QtWaylandGlobal", "/usr/include", "/usr/include/c++/12", "/usr/include/x86_64-linux-gnu/c++/12", "/usr/include/c++/12/backward", "/usr/lib/gcc/x86_64-linux-gnu/12/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/CMakeFiles/treeland-integration_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt6/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/CMakeFiles/treeland-integration_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp", "Mu", null], ["/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}