# Generated by CMake. Changes will be overwritten.
/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp
 mmc:Q_OBJECT
 mid:personalizationmanager.moc
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.cpp
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.h
 mdp:/usr/include/GL/gl.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/any
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cassert
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/climits
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/inttypes.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/math.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wayland-client-core.h
 mdp:/usr/include/wayland-util.h
 mdp:/usr/include/wayland-version.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-fast.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-logb.h
 mdp:/usr/include/x86_64-linux-gnu/bits/iscanonical.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/math-vector.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qconfig_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qglobal_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qtcore-config_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QByteArray
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QDeadlineTimer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QElapsedTimer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QEvent
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QList
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QLoggingCategory
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMap
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMargins
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMutex
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QPoint
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QPointer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QReadWriteLock
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QRect
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QSize
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QSizeF
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QString
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QVariant
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QWaitCondition
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20iterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q23utility.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication_platform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreevent.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdeadlinetimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qelapsedtimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qeventloop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qline.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qloggingcategory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmutex.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnativeinterface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoperatingsystemversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qreadwritelock.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qspan.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtsan_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtyperevision.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversionnumber.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qwaitcondition.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qtgui-config_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qtguiglobal_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qxkbcommon_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatforminputcontextfactory_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatformkeymapper.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatformopenglcontext.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatformsurface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatformwindow.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qplatformwindow_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QEventPoint
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QGuiApplication
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QIcon
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QImage
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QSurfaceFormat
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QTransform
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/QWindow
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qbitmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qcolor.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qcursor.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qeventpoint.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication_platform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qicon.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qimage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qinputdevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qinputmethod.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qopengl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qopenglcontext.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qopenglcontext_platform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qopenglext.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qpaintdevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qpixelformat.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qpixmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qpointingdevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qpolygon.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qregion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qrgb.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qrgba64.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen_platform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qsurface.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qsurfaceformat.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtransform.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qvector2d.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qvectornd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qwindow.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qwindowdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qtwaylandclient-config_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qtwaylandclientglobal_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qwayland-wayland.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qwaylanddisplay_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qwaylandshellsurface_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qwaylandshm_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/qwaylandwindow_p.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/6.8.0/QtWaylandClient/private/wayland-wayland-client-protocol.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/QWaylandClientExtension
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclient-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qwaylandclientextension.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandGlobal/6.8.0/QtWaylandGlobal/private/qtwaylandglobal-config_p.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/include/xkbcommon/xkbcommon-compat.h
 mdp:/usr/include/xkbcommon/xkbcommon-keysyms.h
 mdp:/usr/include/xkbcommon/xkbcommon-names.h
 mdp:/usr/include/xkbcommon/xkbcommon.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-protocol.c
/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.cpp
/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationwindow.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/inttypes.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/math.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wayland-client-core.h
 mdp:/usr/include/wayland-util.h
 mdp:/usr/include/wayland-version.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-fast.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-logb.h
 mdp:/usr/include/x86_64-linux-gnu/bits/iscanonical.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/math-vector.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QByteArray
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QString
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/QWaylandClientExtension
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclient-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qwaylandclientextension.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/treeland-integration_autogen/moc_predefs.h
 mdp:/home/<USER>/Desktop/myrepo/dde-launchpad/src/treelandintegration/personalizationmanager.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/12/algorithm
 mdp:/usr/include/c++/12/array
 mdp:/usr/include/c++/12/atomic
 mdp:/usr/include/c++/12/backward/auto_ptr.h
 mdp:/usr/include/c++/12/backward/binders.h
 mdp:/usr/include/c++/12/bit
 mdp:/usr/include/c++/12/bits/algorithmfwd.h
 mdp:/usr/include/c++/12/bits/align.h
 mdp:/usr/include/c++/12/bits/alloc_traits.h
 mdp:/usr/include/c++/12/bits/allocated_ptr.h
 mdp:/usr/include/c++/12/bits/allocator.h
 mdp:/usr/include/c++/12/bits/atomic_base.h
 mdp:/usr/include/c++/12/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/12/bits/basic_string.h
 mdp:/usr/include/c++/12/bits/basic_string.tcc
 mdp:/usr/include/c++/12/bits/char_traits.h
 mdp:/usr/include/c++/12/bits/charconv.h
 mdp:/usr/include/c++/12/bits/chrono.h
 mdp:/usr/include/c++/12/bits/concept_check.h
 mdp:/usr/include/c++/12/bits/cpp_type_traits.h
 mdp:/usr/include/c++/12/bits/cxxabi_forced.h
 mdp:/usr/include/c++/12/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/12/bits/enable_special_members.h
 mdp:/usr/include/c++/12/bits/erase_if.h
 mdp:/usr/include/c++/12/bits/exception.h
 mdp:/usr/include/c++/12/bits/exception_defines.h
 mdp:/usr/include/c++/12/bits/exception_ptr.h
 mdp:/usr/include/c++/12/bits/functexcept.h
 mdp:/usr/include/c++/12/bits/functional_hash.h
 mdp:/usr/include/c++/12/bits/hash_bytes.h
 mdp:/usr/include/c++/12/bits/hashtable.h
 mdp:/usr/include/c++/12/bits/hashtable_policy.h
 mdp:/usr/include/c++/12/bits/invoke.h
 mdp:/usr/include/c++/12/bits/ios_base.h
 mdp:/usr/include/c++/12/bits/list.tcc
 mdp:/usr/include/c++/12/bits/locale_classes.h
 mdp:/usr/include/c++/12/bits/locale_classes.tcc
 mdp:/usr/include/c++/12/bits/localefwd.h
 mdp:/usr/include/c++/12/bits/memoryfwd.h
 mdp:/usr/include/c++/12/bits/move.h
 mdp:/usr/include/c++/12/bits/nested_exception.h
 mdp:/usr/include/c++/12/bits/new_allocator.h
 mdp:/usr/include/c++/12/bits/node_handle.h
 mdp:/usr/include/c++/12/bits/ostream_insert.h
 mdp:/usr/include/c++/12/bits/parse_numbers.h
 mdp:/usr/include/c++/12/bits/postypes.h
 mdp:/usr/include/c++/12/bits/predefined_ops.h
 mdp:/usr/include/c++/12/bits/ptr_traits.h
 mdp:/usr/include/c++/12/bits/range_access.h
 mdp:/usr/include/c++/12/bits/refwrap.h
 mdp:/usr/include/c++/12/bits/shared_ptr.h
 mdp:/usr/include/c++/12/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/12/bits/shared_ptr_base.h
 mdp:/usr/include/c++/12/bits/specfun.h
 mdp:/usr/include/c++/12/bits/std_abs.h
 mdp:/usr/include/c++/12/bits/std_function.h
 mdp:/usr/include/c++/12/bits/stl_algo.h
 mdp:/usr/include/c++/12/bits/stl_algobase.h
 mdp:/usr/include/c++/12/bits/stl_bvector.h
 mdp:/usr/include/c++/12/bits/stl_construct.h
 mdp:/usr/include/c++/12/bits/stl_function.h
 mdp:/usr/include/c++/12/bits/stl_heap.h
 mdp:/usr/include/c++/12/bits/stl_iterator.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/12/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/12/bits/stl_list.h
 mdp:/usr/include/c++/12/bits/stl_map.h
 mdp:/usr/include/c++/12/bits/stl_multimap.h
 mdp:/usr/include/c++/12/bits/stl_numeric.h
 mdp:/usr/include/c++/12/bits/stl_pair.h
 mdp:/usr/include/c++/12/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/12/bits/stl_relops.h
 mdp:/usr/include/c++/12/bits/stl_tempbuf.h
 mdp:/usr/include/c++/12/bits/stl_tree.h
 mdp:/usr/include/c++/12/bits/stl_uninitialized.h
 mdp:/usr/include/c++/12/bits/stl_vector.h
 mdp:/usr/include/c++/12/bits/stream_iterator.h
 mdp:/usr/include/c++/12/bits/streambuf.tcc
 mdp:/usr/include/c++/12/bits/streambuf_iterator.h
 mdp:/usr/include/c++/12/bits/string_view.tcc
 mdp:/usr/include/c++/12/bits/stringfwd.h
 mdp:/usr/include/c++/12/bits/uniform_int_dist.h
 mdp:/usr/include/c++/12/bits/unique_ptr.h
 mdp:/usr/include/c++/12/bits/unordered_map.h
 mdp:/usr/include/c++/12/bits/uses_allocator.h
 mdp:/usr/include/c++/12/bits/utility.h
 mdp:/usr/include/c++/12/bits/vector.tcc
 mdp:/usr/include/c++/12/cctype
 mdp:/usr/include/c++/12/cerrno
 mdp:/usr/include/c++/12/chrono
 mdp:/usr/include/c++/12/clocale
 mdp:/usr/include/c++/12/cmath
 mdp:/usr/include/c++/12/compare
 mdp:/usr/include/c++/12/cstddef
 mdp:/usr/include/c++/12/cstdint
 mdp:/usr/include/c++/12/cstdio
 mdp:/usr/include/c++/12/cstdlib
 mdp:/usr/include/c++/12/cstring
 mdp:/usr/include/c++/12/ctime
 mdp:/usr/include/c++/12/cwchar
 mdp:/usr/include/c++/12/debug/assertions.h
 mdp:/usr/include/c++/12/debug/debug.h
 mdp:/usr/include/c++/12/exception
 mdp:/usr/include/c++/12/ext/aligned_buffer.h
 mdp:/usr/include/c++/12/ext/alloc_traits.h
 mdp:/usr/include/c++/12/ext/atomicity.h
 mdp:/usr/include/c++/12/ext/concurrence.h
 mdp:/usr/include/c++/12/ext/numeric_traits.h
 mdp:/usr/include/c++/12/ext/string_conversions.h
 mdp:/usr/include/c++/12/ext/type_traits.h
 mdp:/usr/include/c++/12/functional
 mdp:/usr/include/c++/12/initializer_list
 mdp:/usr/include/c++/12/iosfwd
 mdp:/usr/include/c++/12/iterator
 mdp:/usr/include/c++/12/limits
 mdp:/usr/include/c++/12/list
 mdp:/usr/include/c++/12/map
 mdp:/usr/include/c++/12/memory
 mdp:/usr/include/c++/12/new
 mdp:/usr/include/c++/12/numeric
 mdp:/usr/include/c++/12/optional
 mdp:/usr/include/c++/12/pstl/execution_defs.h
 mdp:/usr/include/c++/12/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/12/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/12/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/12/ratio
 mdp:/usr/include/c++/12/stdexcept
 mdp:/usr/include/c++/12/streambuf
 mdp:/usr/include/c++/12/string
 mdp:/usr/include/c++/12/string_view
 mdp:/usr/include/c++/12/system_error
 mdp:/usr/include/c++/12/tr1/bessel_function.tcc
 mdp:/usr/include/c++/12/tr1/beta_function.tcc
 mdp:/usr/include/c++/12/tr1/ell_integral.tcc
 mdp:/usr/include/c++/12/tr1/exp_integral.tcc
 mdp:/usr/include/c++/12/tr1/gamma.tcc
 mdp:/usr/include/c++/12/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/12/tr1/legendre_function.tcc
 mdp:/usr/include/c++/12/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/12/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/12/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/12/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/12/tr1/special_function_util.h
 mdp:/usr/include/c++/12/tuple
 mdp:/usr/include/c++/12/type_traits
 mdp:/usr/include/c++/12/typeinfo
 mdp:/usr/include/c++/12/unordered_map
 mdp:/usr/include/c++/12/utility
 mdp:/usr/include/c++/12/variant
 mdp:/usr/include/c++/12/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtgui-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/QWaylandClientExtension
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclient-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qtwaylandclientglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtWaylandClient/qwaylandclientextension.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h
/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/wayland-treeland-personalization-manager-v1-client-protocol.h
/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/treelandintegration/qwayland-treeland-personalization-manager-v1.h
