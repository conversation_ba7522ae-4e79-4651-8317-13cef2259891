# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDSG_DATA_DIR=\"/usr/share/dsg\" -DDSYSINFO_PREFIX=\"\" -DPREFIX=\"/usr\" -DQT_CORE_LIB -DQT_DBUS_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_SVG_LIB -DQT_XML_LIB

CXX_INCLUDES = -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils -I/home/<USER>/Desktop/myrepo/dde-launchpad/src/utils -I/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/launcher-utils_autogen/include -isystem /usr/include/x86_64-linux-gnu/qt6/QtCore -isystem /usr/include/x86_64-linux-gnu/qt6 -isystem /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++ -isystem /usr/include/x86_64-linux-gnu/qt6/QtGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtSvg -isystem /usr/include/dtk6/DGui -isystem /usr/include/x86_64-linux-gnu/qt6/QtNetwork -isystem /usr/include/dtk6/DCore -isystem /usr/include/x86_64-linux-gnu/qt6/QtDBus -isystem /usr/include/x86_64-linux-gnu/qt6/QtXml -isystem /usr/include/dtk6/DLog

CXX_FLAGS = -g -O2 -fstack-protector-strong -fstack-clash-protection -Wformat -Werror=format-security -fcf-protection -Wall -Wdate-time -D_FORTIFY_SOURCE=2 -std=gnu++17 -fPIC -fPIC

