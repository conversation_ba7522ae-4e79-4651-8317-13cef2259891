# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu

# Include any dependencies generated for this target.
include src/utils/CMakeFiles/launcher-utils.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.make

# Include the progress variables for this target.
include src/utils/CMakeFiles/launcher-utils.dir/progress.make

# Include the compile flags for this target's objects.
include src/utils/CMakeFiles/launcher-utils.dir/flags.make

src/utils/launcher-utils_autogen/timestamp: /usr/lib/qt6/libexec/moc
src/utils/launcher-utils_autogen/timestamp: src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target launcher-utils"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/cmake -E cmake_autogen /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils_autogen.dir/AutogenInfo.json None
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/cmake -E touch /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/launcher-utils_autogen/timestamp

src/utils/CMakeFiles/launcher-utils.dir/codegen:
.PHONY : src/utils/CMakeFiles/launcher-utils.dir/codegen

src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/flags.make
src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o: src/utils/launcher-utils_autogen/mocs_compilation.cpp
src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o -MF CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/launcher-utils_autogen/mocs_compilation.cpp

src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/launcher-utils_autogen/mocs_compilation.cpp > CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.i

src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/launcher-utils_autogen/mocs_compilation.cpp -o CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.s

src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/flags.make
src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/categoryutils.cpp
src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o -MF CMakeFiles/launcher-utils.dir/categoryutils.cpp.o.d -o CMakeFiles/launcher-utils.dir/categoryutils.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/categoryutils.cpp

src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-utils.dir/categoryutils.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/categoryutils.cpp > CMakeFiles/launcher-utils.dir/categoryutils.cpp.i

src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-utils.dir/categoryutils.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/categoryutils.cpp -o CMakeFiles/launcher-utils.dir/categoryutils.cpp.s

src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/flags.make
src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/iconutils.cpp
src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o -MF CMakeFiles/launcher-utils.dir/iconutils.cpp.o.d -o CMakeFiles/launcher-utils.dir/iconutils.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/iconutils.cpp

src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-utils.dir/iconutils.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/iconutils.cpp > CMakeFiles/launcher-utils.dir/iconutils.cpp.i

src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-utils.dir/iconutils.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/iconutils.cpp -o CMakeFiles/launcher-utils.dir/iconutils.cpp.s

src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/flags.make
src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o: /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/blurhash.cpp
src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o: src/utils/CMakeFiles/launcher-utils.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o -MF CMakeFiles/launcher-utils.dir/blurhash.cpp.o.d -o CMakeFiles/launcher-utils.dir/blurhash.cpp.o -c /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/blurhash.cpp

src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/launcher-utils.dir/blurhash.cpp.i"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/blurhash.cpp > CMakeFiles/launcher-utils.dir/blurhash.cpp.i

src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/launcher-utils.dir/blurhash.cpp.s"
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils/blurhash.cpp -o CMakeFiles/launcher-utils.dir/blurhash.cpp.s

launcher-utils: src/utils/CMakeFiles/launcher-utils.dir/launcher-utils_autogen/mocs_compilation.cpp.o
launcher-utils: src/utils/CMakeFiles/launcher-utils.dir/categoryutils.cpp.o
launcher-utils: src/utils/CMakeFiles/launcher-utils.dir/iconutils.cpp.o
launcher-utils: src/utils/CMakeFiles/launcher-utils.dir/blurhash.cpp.o
launcher-utils: src/utils/CMakeFiles/launcher-utils.dir/build.make
.PHONY : launcher-utils

# Rule to build all files generated by this target.
src/utils/CMakeFiles/launcher-utils.dir/build: launcher-utils
.PHONY : src/utils/CMakeFiles/launcher-utils.dir/build

src/utils/CMakeFiles/launcher-utils.dir/clean:
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils && $(CMAKE_COMMAND) -P CMakeFiles/launcher-utils.dir/cmake_clean.cmake
.PHONY : src/utils/CMakeFiles/launcher-utils.dir/clean

src/utils/CMakeFiles/launcher-utils.dir/depend: src/utils/launcher-utils_autogen/timestamp
	cd /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Desktop/myrepo/dde-launchpad /home/<USER>/Desktop/myrepo/dde-launchpad/src/utils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/src/utils/CMakeFiles/launcher-utils.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/utils/CMakeFiles/launcher-utils.dir/depend

