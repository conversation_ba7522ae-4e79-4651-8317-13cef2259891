CMakeFiles/launchpadcommon.dir/launchercontroller.cpp.o: \
 /home/<USER>/Desktop/myrepo/dde-launchpad/launchercontroller.cpp \
 /usr/include/stdc-predef.h \
 /home/<USER>/Desktop/myrepo/dde-launchpad/launchercontroller.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqml.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqmlprivate.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qjsprimitivevalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qtqmlglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h \
 /usr/include/c++/12/type_traits \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/os_defines.h \
 /usr/include/features.h /usr/include/features-time64.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/cpu_defines.h \
 /usr/include/c++/12/pstl/pstl_config.h /usr/include/c++/12/cstddef \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stddef.h \
 /usr/include/c++/12/utility /usr/include/c++/12/bits/stl_relops.h \
 /usr/include/c++/12/bits/stl_pair.h /usr/include/c++/12/bits/move.h \
 /usr/include/c++/12/bits/utility.h /usr/include/c++/12/initializer_list \
 /usr/include/c++/12/cstdint \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h /usr/include/assert.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdbool.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationdefinitions.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h \
 /usr/include/c++/12/version \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h \
 /usr/include/c++/12/limits \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h \
 /usr/include/c++/12/atomic /usr/include/c++/12/bits/atomic_base.h \
 /usr/include/c++/12/bits/atomic_lockfree_defines.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h \
 /usr/include/c++/12/optional /usr/include/c++/12/exception \
 /usr/include/c++/12/bits/exception.h \
 /usr/include/c++/12/bits/exception_ptr.h \
 /usr/include/c++/12/bits/exception_defines.h \
 /usr/include/c++/12/bits/cxxabi_init_exception.h \
 /usr/include/c++/12/typeinfo /usr/include/c++/12/bits/hash_bytes.h \
 /usr/include/c++/12/new /usr/include/c++/12/bits/nested_exception.h \
 /usr/include/c++/12/bits/enable_special_members.h \
 /usr/include/c++/12/bits/functional_hash.h \
 /usr/include/c++/12/bits/stl_construct.h \
 /usr/include/c++/12/bits/stl_iterator_base_types.h \
 /usr/include/c++/12/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/12/bits/concept_check.h \
 /usr/include/c++/12/debug/assertions.h /usr/include/c++/12/tuple \
 /usr/include/c++/12/bits/uses_allocator.h \
 /usr/include/c++/12/bits/invoke.h /usr/include/c++/12/variant \
 /usr/include/c++/12/bits/parse_numbers.h \
 /usr/include/c++/12/ext/numeric_traits.h \
 /usr/include/c++/12/bits/cpp_type_traits.h \
 /usr/include/c++/12/ext/type_traits.h \
 /usr/include/c++/12/ext/aligned_buffer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h \
 /usr/include/c++/12/cmath /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/c++/12/bits/std_abs.h /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/select2.h \
 /usr/include/x86_64-linux-gnu/bits/select-decl.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib.h \
 /usr/include/c++/12/bits/specfun.h \
 /usr/include/c++/12/bits/stl_algobase.h \
 /usr/include/c++/12/bits/functexcept.h \
 /usr/include/c++/12/bits/stl_iterator.h \
 /usr/include/c++/12/bits/ptr_traits.h /usr/include/c++/12/debug/debug.h \
 /usr/include/c++/12/bits/predefined_ops.h \
 /usr/include/c++/12/tr1/gamma.tcc \
 /usr/include/c++/12/tr1/special_function_util.h \
 /usr/include/c++/12/tr1/bessel_function.tcc \
 /usr/include/c++/12/tr1/beta_function.tcc \
 /usr/include/c++/12/tr1/ell_integral.tcc \
 /usr/include/c++/12/tr1/exp_integral.tcc \
 /usr/include/c++/12/tr1/hypergeometric.tcc \
 /usr/include/c++/12/tr1/legendre_function.tcc \
 /usr/include/c++/12/tr1/modified_bessel_func.tcc \
 /usr/include/c++/12/tr1/poly_hermite.tcc \
 /usr/include/c++/12/tr1/poly_laguerre.tcc \
 /usr/include/c++/12/tr1/riemann_zeta.tcc \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qtqml-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetworkglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetwork-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetworkexports.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qtqmlexports.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qjsnumbercoercion.h \
 /usr/include/c++/12/cstring /usr/include/string.h /usr/include/strings.h \
 /usr/include/x86_64-linux-gnu/bits/strings_fortified.h \
 /usr/include/x86_64-linux-gnu/bits/string_fortified.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcomparehelpers.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20type_traits.h \
 /usr/include/c++/12/functional /usr/include/c++/12/bits/stl_function.h \
 /usr/include/c++/12/backward/binders.h \
 /usr/include/c++/12/bits/refwrap.h \
 /usr/include/c++/12/bits/std_function.h \
 /usr/include/c++/12/unordered_map /usr/include/c++/12/bits/allocator.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++allocator.h \
 /usr/include/c++/12/bits/new_allocator.h \
 /usr/include/c++/12/bits/memoryfwd.h \
 /usr/include/c++/12/ext/alloc_traits.h \
 /usr/include/c++/12/bits/alloc_traits.h \
 /usr/include/c++/12/bits/hashtable.h \
 /usr/include/c++/12/bits/hashtable_policy.h \
 /usr/include/c++/12/bits/node_handle.h \
 /usr/include/c++/12/bits/unordered_map.h \
 /usr/include/c++/12/bits/range_access.h \
 /usr/include/c++/12/bits/erase_if.h /usr/include/c++/12/vector \
 /usr/include/c++/12/bits/stl_uninitialized.h \
 /usr/include/c++/12/bits/stl_vector.h \
 /usr/include/c++/12/bits/stl_bvector.h \
 /usr/include/c++/12/bits/vector.tcc /usr/include/c++/12/array \
 /usr/include/c++/12/compare /usr/include/c++/12/bits/stl_algo.h \
 /usr/include/c++/12/bits/algorithmfwd.h \
 /usr/include/c++/12/bits/stl_heap.h \
 /usr/include/c++/12/bits/stl_tempbuf.h \
 /usr/include/c++/12/bits/uniform_int_dist.h /usr/include/c++/12/cstdlib \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qxptype_traits.h \
 /usr/include/c++/12/iterator /usr/include/c++/12/iosfwd \
 /usr/include/c++/12/bits/stringfwd.h /usr/include/c++/12/bits/postypes.h \
 /usr/include/c++/12/cwchar /usr/include/wchar.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/wchar2-decl.h \
 /usr/include/x86_64-linux-gnu/bits/wchar2.h \
 /usr/include/c++/12/bits/stream_iterator.h \
 /usr/include/c++/12/bits/streambuf_iterator.h \
 /usr/include/c++/12/streambuf /usr/include/c++/12/bits/localefwd.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/c++locale.h \
 /usr/include/c++/12/clocale /usr/include/locale.h \
 /usr/include/x86_64-linux-gnu/bits/locale.h /usr/include/c++/12/cctype \
 /usr/include/ctype.h /usr/include/c++/12/bits/ios_base.h \
 /usr/include/c++/12/ext/atomicity.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/x86_64-linux-gnu/bits/sched.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/timex.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/x86_64-linux-gnu/bits/setjmp.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/atomic_word.h \
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
 /usr/include/c++/12/bits/locale_classes.h /usr/include/c++/12/string \
 /usr/include/c++/12/bits/char_traits.h \
 /usr/include/c++/12/bits/ostream_insert.h \
 /usr/include/c++/12/bits/cxxabi_forced.h \
 /usr/include/c++/12/bits/basic_string.h /usr/include/c++/12/string_view \
 /usr/include/c++/12/bits/string_view.tcc \
 /usr/include/c++/12/ext/string_conversions.h /usr/include/c++/12/cstdio \
 /usr/include/stdio.h /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/stdio2-decl.h \
 /usr/include/x86_64-linux-gnu/bits/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/stdio2.h /usr/include/c++/12/cerrno \
 /usr/include/errno.h /usr/include/x86_64-linux-gnu/bits/errno.h \
 /usr/include/linux/errno.h /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/12/bits/charconv.h \
 /usr/include/c++/12/bits/basic_string.tcc \
 /usr/include/c++/12/bits/locale_classes.tcc \
 /usr/include/c++/12/system_error \
 /usr/include/x86_64-linux-gnu/c++/12/bits/error_constants.h \
 /usr/include/c++/12/stdexcept /usr/include/c++/12/bits/streambuf.tcc \
 /usr/include/c++/12/memory \
 /usr/include/c++/12/bits/stl_raw_storage_iter.h \
 /usr/include/c++/12/bits/align.h /usr/include/c++/12/bit \
 /usr/include/c++/12/bits/unique_ptr.h \
 /usr/include/c++/12/bits/shared_ptr.h \
 /usr/include/c++/12/bits/shared_ptr_base.h \
 /usr/include/c++/12/bits/allocated_ptr.h \
 /usr/include/c++/12/ext/concurrence.h \
 /usr/include/c++/12/bits/shared_ptr_atomic.h \
 /usr/include/c++/12/backward/auto_ptr.h \
 /usr/include/c++/12/pstl/glue_memory_defs.h \
 /usr/include/c++/12/pstl/execution_defs.h /usr/include/c++/12/algorithm \
 /usr/include/c++/12/pstl/glue_algorithm_defs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h \
 /usr/include/c++/12/stdlib.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfloat16.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h \
 /usr/include/c++/12/numeric /usr/include/c++/12/bits/stl_numeric.h \
 /usr/include/c++/12/pstl/glue_numeric_defs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionaltools_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h \
 /usr/include/c++/12/list /usr/include/c++/12/bits/stl_list.h \
 /usr/include/c++/12/bits/list.tcc /usr/include/c++/12/map \
 /usr/include/c++/12/bits/stl_tree.h /usr/include/c++/12/bits/stl_map.h \
 /usr/include/c++/12/bits/stl_multimap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h \
 /usr/include/c++/12/climits \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/syslimits.h \
 /usr/include/limits.h /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h \
 /usr/include/c++/12/chrono /usr/include/c++/12/bits/chrono.h \
 /usr/include/c++/12/ratio /usr/include/c++/12/ctime \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q23utility.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qjsvalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqmllist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqmlparserstatus.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqmlpropertyvaluesource.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatetime.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcalendar.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetaobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qurl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qversionnumber.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qspan.h \
 /usr/include/c++/12/cassert \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20iterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtyperevision.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQml/qqmlregistration.h \
 /usr/include/x86_64-linux-gnu/qt6/QtQmlIntegration/qqmlintegration.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QCommandLineOption \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcommandlineoption.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QFont \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qfont.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qendian.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtgui-config.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtguiexports.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qwindowdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QObject \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QDir \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdir.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdirlisting.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfiledevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfile.h \
 /usr/include/c++/12/filesystem /usr/include/c++/12/bits/fs_fwd.h \
 /usr/include/c++/12/bits/fs_path.h /usr/include/c++/12/locale \
 /usr/include/c++/12/bits/locale_facets.h /usr/include/c++/12/cwctype \
 /usr/include/wctype.h /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_base.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/ctype_inline.h \
 /usr/include/c++/12/bits/locale_facets.tcc \
 /usr/include/c++/12/bits/locale_facets_nonio.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/time_members.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/messages_members.h \
 /usr/include/libintl.h /usr/include/c++/12/bits/codecvt.h \
 /usr/include/c++/12/bits/locale_facets_nonio.tcc \
 /usr/include/c++/12/bits/locale_conv.h /usr/include/c++/12/iomanip \
 /usr/include/c++/12/bits/quoted_string.h /usr/include/c++/12/sstream \
 /usr/include/c++/12/istream /usr/include/c++/12/ios \
 /usr/include/c++/12/bits/basic_ios.h \
 /usr/include/c++/12/bits/basic_ios.tcc /usr/include/c++/12/ostream \
 /usr/include/c++/12/bits/ostream.tcc \
 /usr/include/c++/12/bits/istream.tcc \
 /usr/include/c++/12/bits/sstream.tcc /usr/include/c++/12/codecvt \
 /usr/include/c++/12/bits/fs_dir.h /usr/include/c++/12/bits/fs_ops.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfileinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimezone.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QTimer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QSettings \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsettings.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QStandardPaths \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstandardpaths.h \
 /usr/include/dtk6/DGui/DGuiApplicationHelper \
 /usr/include/dtk6/DGui/dguiapplicationhelper.h \
 /usr/include/dtk6/DGui/dtkgui_global.h \
 /usr/include/dtk6/DCore/dtkcore_global.h \
 /usr/include/dtk6/DCore/dtkcore_config.h /usr/include/dtk6/DGui/DPalette \
 /usr/include/dtk6/DGui/dpalette.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QDebug \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QPalette \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpalette.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qcolor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qrgb.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qrgba64.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qbrush.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qimage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpaintdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpixelformat.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtransform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpolygon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qregion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qline.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpixmap.h \
 /usr/include/dtk6/DCore/DObject /usr/include/dtk6/DCore/dobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QScopedPointer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QGuiApplication \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdeadlinetimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qelapsedtimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeventloop.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnativeinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication_platform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qinputmethod.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication_platform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qguiapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QCommandLineParser \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcommandlineparser.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcommandlineoption.h \
 /home/<USER>/Desktop/myrepo/dde-launchpad/obj-x86_64-linux-gnu/launcher1adaptor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QObject \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/QtDBus \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/QtDBusDepends \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QtCore \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QtCoreDepends \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20algorithm.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20chrono.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20functional.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20iterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20map.h \
 /usr/include/c++/12/memory_resource /usr/include/c++/12/shared_mutex \
 /usr/include/c++/12/bits/std_mutex.h \
 /usr/include/c++/12/bits/uses_allocator_args.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20memory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20vector.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q23functional.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q23utility.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q26numeric.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstracteventdispatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractitemmodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractnativeeventfilter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractitemmodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qanimationgroup.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qapplicationstatic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QMutex \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmutex.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtsan_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qassert.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qassociativeiterable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qatomicscopedvaluerollback.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbitarray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbuffer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraymatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q20algorithm.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcache.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcalendar.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborarray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborvalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborcommon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qregularexpression.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/quuid.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborcommon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcbormap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamreader.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamwriter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamreader.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborstreamwriter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcborvalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qchronotimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qproperty.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyprivate.h \
 /usr/include/c++/12/experimental/source_location \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcollator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qconcatenatetablesproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qconstructormacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreapplication.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcoreevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcryptographichash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdarwinhelpers.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdatetime.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdeadlinetimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdiriterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdir.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qdirlisting.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeasingcurve.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qelapsedtimer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qendian.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeventloop.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qexception.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qexceptionhandling.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfactoryinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfile.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfiledevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfileinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfileselector.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QStringList \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfilesystemwatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfunctionpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfuture.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfutureinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmutex.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qresultstore.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfuture_impl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthreadpool.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthread.h \
 /usr/include/c++/12/future /usr/include/c++/12/mutex \
 /usr/include/c++/12/bits/unique_lock.h \
 /usr/include/c++/12/condition_variable \
 /usr/include/c++/12/bits/atomic_futex.h \
 /usr/include/c++/12/bits/std_thread.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrunnable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qexception.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpromise.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfutureinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfuturesynchronizer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfuture.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qfuturewatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qidentityproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qabstractproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qitemselectionmodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonarray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonvalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsondocument.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonvalue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlibrary.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlibraryinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qline.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlockfile.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qloggingcategory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmalloc.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmargins.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmessageauthenticationcode.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qcryptographichash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetaobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmimedata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmimedatabase.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmimetype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmimetype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qminmax.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnativeinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectcleanuphandler.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qoperatingsystemversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qoverload.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qparallelanimationgroup.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qanimationgroup.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpauseanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpermissions.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qplugin.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qjsonobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpluginloader.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlibrary.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qplugin.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpoint.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qprocess.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpromise.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qproperty.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qeasingcurve.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qpropertyprivate.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qqueue.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrandom.h \
 /usr/include/c++/12/random /usr/include/c++/12/bits/random.h \
 /usr/include/x86_64-linux-gnu/c++/12/bits/opt_random.h \
 /usr/include/c++/12/bits/random.tcc \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qreadwritelock.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrect.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qregularexpression.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qresource.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qresultstore.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qrunnable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsavefile.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedvaluerollback.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsemaphore.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsequentialanimationgroup.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsequentialiterable.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedmemory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtipccommon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsignalmapper.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsimd.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/immintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/x86gprintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/ia32intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/adxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/bmiintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/bmi2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/cetintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/cldemoteintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/clflushoptintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/clwbintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/clzerointrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/enqcmdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/fxsrintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/lzcntintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/lwpintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/movdirintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/mwaitintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/mwaitxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/pconfigintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/popcntintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/pkuintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/rdseedintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/rtmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/serializeintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/sgxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/tbmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/tsxldtrkintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/uintrintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/waitpkgintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/wbnoinvdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xsaveintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xsavecintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xsaveoptintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xsavesintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xtestintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/hresetintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/mmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/xmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/mm_malloc.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/emmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/pmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/tmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/smmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/wmmintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avxintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avxvnniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512erintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512pfintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512cdintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512dqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vlbwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vldqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512ifmaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512ifmavlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmiintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmivlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx5124fmapsintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx5124vnniwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vpopcntdqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmi2intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vbmi2vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vnniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vnnivlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vpopcntdqvlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bitalgintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vp2intersectintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512vp2intersectvlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fp16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512fp16vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/shaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/fmaintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/f16cintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/gfniintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/vaesintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/vpclmulqdqintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bf16vlintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/avx512bf16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/amxtileintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/amxint8intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/amxbf16intrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/prfchwintrin.h \
 /usr/lib/gcc/x86_64-linux-gnu/12/include/keylockerintrin.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsize.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsocketnotifier.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsortfilterproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qspan.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstack.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstaticlatin1stringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qlatin1stringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstorageinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringbuilder.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlistmodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qswap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemsemaphore.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtclasshelpermacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfiginclude.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtdeprecationmarkers.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtemporarydir.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtemporaryfile.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtenvironmentvariables.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtextboundaryfinder.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthread.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthreadpool.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qthreadstorage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimeline.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtimezone.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtipccommon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtnoop.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtpreprocessorsupport.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtranslator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtransposeproxymodel.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtresource.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtsymbolmacros.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qttranslation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qttypetraits.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtversionchecks.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtyperevision.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtypes.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qurl.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qurlquery.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/quuid.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantanimation.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvarianthash.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QHash \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QVariant \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QString \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantlist.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QList \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QMap \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvector.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qversionnumber.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qwaitcondition.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QDeadlineTimer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qwineventnotifier.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qxmlstream.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qxpfunctional.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q23functional.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qyieldcpu.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qtdbusglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qtdbusexports.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusabstractadaptor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qtdbusglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusabstractinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusmessage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuserror.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusextratypes.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusconnection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuspendingcall.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusargument.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusconnection.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusconnectioninterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusabstractinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusreply.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuspendingreply.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusargument.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuscontext.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuserror.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusextratypes.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusinterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusmessage.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusmetatype.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuspendingcall.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbuspendingreply.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusreply.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusserver.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusservicewatcher.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusunixfiledescriptor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qdbusvirtualobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/qtdbusversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/QDBusMessage \
 /usr/include/x86_64-linux-gnu/qt6/QtDBus/QDBusConnection \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qguiapplication_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qtguiglobal_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qglobal_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qglobal_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qconfig_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qtcore-config_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qoperatingsystemversion.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qtgui-config_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qicon.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QPointF \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qcoreapplication_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qreadwritelock.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qtranslator.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsettings.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qloggingcategory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qobject_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qproperty_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qglobal_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qproperty.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qmetaobject.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedvaluerollback.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QVarLengthArray \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qlocking_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qnativeinterface_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qloggingcategory.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qnumeric_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qsimd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/q26numeric.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/6.8.0/QtCore/private/qthread_p.h \
 /usr/lib/x86_64-linux-gnu/qt6/mkspecs/linux-g++/qplatformdefs.h \
 /usr/include/unistd.h /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
 /usr/include/x86_64-linux-gnu/bits/environments.h \
 /usr/include/x86_64-linux-gnu/bits/confname.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
 /usr/include/x86_64-linux-gnu/bits/unistd.h \
 /usr/include/x86_64-linux-gnu/bits/unistd-decl.h \
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h \
 /usr/include/linux/close_range.h /usr/include/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/dirent_ext.h /usr/include/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl-linux.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h \
 /usr/include/linux/falloc.h /usr/include/x86_64-linux-gnu/bits/stat.h \
 /usr/include/x86_64-linux-gnu/bits/struct_stat.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl2.h /usr/include/grp.h \
 /usr/include/pwd.h /usr/include/signal.h \
 /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
 /usr/include/x86_64-linux-gnu/bits/signum-arch.h \
 /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
 /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
 /usr/include/x86_64-linux-gnu/bits/sigaction.h \
 /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
 /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
 /usr/include/x86_64-linux-gnu/sys/ucontext.h \
 /usr/include/x86_64-linux-gnu/bits/sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/sigstksz.h \
 /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/sigthread.h \
 /usr/include/x86_64-linux-gnu/bits/signal_ext.h \
 /usr/include/x86_64-linux-gnu/sys/ioctl.h \
 /usr/include/x86_64-linux-gnu/bits/ioctls.h \
 /usr/include/x86_64-linux-gnu/asm/ioctls.h \
 /usr/include/asm-generic/ioctls.h /usr/include/linux/ioctl.h \
 /usr/include/x86_64-linux-gnu/asm/ioctl.h \
 /usr/include/asm-generic/ioctl.h \
 /usr/include/x86_64-linux-gnu/bits/ioctl-types.h \
 /usr/include/x86_64-linux-gnu/sys/ttydefaults.h \
 /usr/include/x86_64-linux-gnu/sys/ipc.h \
 /usr/include/x86_64-linux-gnu/bits/ipctypes.h \
 /usr/include/x86_64-linux-gnu/bits/ipc.h \
 /usr/include/x86_64-linux-gnu/bits/ipc-perm.h \
 /usr/include/x86_64-linux-gnu/sys/time.h \
 /usr/include/x86_64-linux-gnu/sys/shm.h \
 /usr/include/x86_64-linux-gnu/bits/shm.h \
 /usr/include/x86_64-linux-gnu/bits/shmlba.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_shmid_ds.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_shmid64_ds.h \
 /usr/include/x86_64-linux-gnu/sys/socket.h \
 /usr/include/x86_64-linux-gnu/bits/socket.h \
 /usr/include/x86_64-linux-gnu/bits/socket_type.h \
 /usr/include/x86_64-linux-gnu/bits/sockaddr.h \
 /usr/include/x86_64-linux-gnu/asm/socket.h \
 /usr/include/asm-generic/socket.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types_64.h \
 /usr/include/asm-generic/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h \
 /usr/include/x86_64-linux-gnu/asm/sockios.h \
 /usr/include/asm-generic/sockios.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h \
 /usr/include/x86_64-linux-gnu/bits/socket2.h \
 /usr/include/x86_64-linux-gnu/sys/stat.h \
 /usr/include/x86_64-linux-gnu/bits/statx.h /usr/include/linux/stat.h \
 /usr/include/linux/types.h /usr/include/x86_64-linux-gnu/asm/types.h \
 /usr/include/asm-generic/types.h /usr/include/asm-generic/int-ll64.h \
 /usr/include/x86_64-linux-gnu/bits/statx-generic.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_statx_timestamp.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_statx.h \
 /usr/include/x86_64-linux-gnu/sys/wait.h \
 /usr/include/x86_64-linux-gnu/bits/types/idtype_t.h \
 /usr/include/netinet/in.h /usr/include/x86_64-linux-gnu/bits/in.h \
 /usr/lib/x86_64-linux-gnu/qt6/mkspecs/common/posix/qplatformdefs.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qstack.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/qwaitcondition.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qwindowsysteminterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QTime \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QEvent \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QAbstractEventDispatcher \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QScreen \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QRect \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QSize \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QSizeF \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QTransform \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qtransform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen_platform.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QWindow \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qwindow.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QMargins \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qsurface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qsurfaceformat.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qcursor.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qbitmap.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QWeakPointer \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QTouchEvent \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qeventpoint.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qvector2d.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qvectornd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qpointingdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qinputdevice.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qscreen.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qkeysequence.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QEventLoop \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/QVector2D \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qvectornd.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qwindowsysteminterface_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qevent_p.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qevent.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/qwindow.h \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/qpa/qwindowsysteminterface.h \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QElapsedTimer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QPointer \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QMutex \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QList \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QWaitCondition \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QAtomicInt \
 /usr/include/x86_64-linux-gnu/qt6/QtCore/QLoggingCategory \
 /usr/include/x86_64-linux-gnu/qt6/QtGui/6.8.0/QtGui/private/qshortcutmap_p.h
