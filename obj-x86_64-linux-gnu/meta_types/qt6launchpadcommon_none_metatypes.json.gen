[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "DebugItem"}, {"name": "QML.Attached", "value": "DebugQuickItem"}], "className": "DebugQuickItem", "lineNumber": 10, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "DebugQuickItem", "signals": [{"access": "public", "index": 0, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "DebugHelper"}, {"name": "QML.Singleton", "value": "true"}], "className": "DebugHelper", "lineNumber": 29, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "qtDebugEnabled", "read": "qtDebugEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "member": "m_useRegularWindow", "name": "useRegularWindow", "notify": "onUseRegularWindowChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "member": "m_avoidLaunchApp", "name": "avoidLaunchApp", "notify": "onAvoidLaunchAppChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_avoidHideWindow", "name": "avoidHideWindow", "notify": "onAvoidHideWindowChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "member": "m_itemBoundingEnabled", "name": "itemBoundingEnabled", "notify": "onItemBoundingEnabledChanged", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "DebugHelper", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "onUseRegularWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 1, "name": "onAvoidLaunchAppChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 2, "name": "onAvoidHideWindowChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "onItemBoundingEnabledChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "debughelper.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "DesktopIntegration"}, {"name": "QML.Singleton", "value": "true"}], "className": "DesktopIntegration", "lineNumber": 15, "methods": [{"access": "public", "index": 6, "name": "currentDE", "returnType": "QString"}, {"access": "public", "index": 7, "name": "isTreeLand", "returnType": "bool"}, {"access": "public", "index": 8, "name": "openSystemSettings", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 9, "name": "launchByDesktopId", "returnType": "void"}, {"access": "public", "arguments": [{"name": "env", "type": "QString"}], "index": 10, "name": "environmentVariable", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 11, "name": "disableScale", "returnType": "double"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}, {"name": "disableScale", "type": "double"}], "index": 12, "name": "setDisableScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "location", "type": "QStandardPaths::StandardLocation"}], "index": 13, "name": "showFolder", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QString"}], "index": 14, "name": "showUrl", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 15, "name": "appIsCompulsoryForDesktop", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 16, "name": "appIsDummyPackage", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 17, "name": "isDockedApp", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 18, "name": "sendToDock", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 19, "name": "removeFromDock", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 20, "name": "isOnDesktop", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 21, "name": "sendToDesktop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 22, "name": "removeFromDesktop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 23, "name": "isAutoStart", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}, {"name": "on", "type": "bool"}], "index": 24, "name": "setAutoStart", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 25, "isCloned": true, "name": "setAutoStart", "returnType": "void"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 26, "name": "shouldSkipConfirmUninstallDialog", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "desktopId", "type": "QString"}], "index": 27, "name": "uninstallApp", "returnType": "void"}, {"access": "public", "index": 28, "name": "scaleFactor", "returnType": "double"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dockPosition", "notify": "dockPositionChanged", "read": "dockPosition", "required": false, "scriptable": true, "stored": true, "type": "Qt::ArrowType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "dockGeometry", "notify": "dockGeometryChanged", "read": "dockGeometry", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "dockSpacing", "notify": "dockSpacingChanged", "read": "dockSpacing", "required": false, "scriptable": true, "stored": true, "type": "uint", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "backgroundUrl", "notify": "backgroundUrlChanged", "read": "backgroundUrl", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "opacity", "notify": "opacityChanged", "read": "opacity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "scaleFactor", "notify": "scaleFactorChanged", "read": "scaleFactor", "required": false, "scriptable": true, "stored": true, "type": "double", "user": false}], "qualifiedClassName": "DesktopIntegration", "signals": [{"access": "public", "index": 0, "name": "dockPositionChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "dockGeometryChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "dockSpacingChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "backgroundUrlChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "opacityChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "scaleFactorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "desktopintegration.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "InputEventItem", "lineNumber": 11, "object": true, "qualifiedClassName": "InputEventItem", "signals": [{"access": "public", "arguments": [{"name": "input", "type": "QString"}], "index": 0, "name": "inputReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "inputeventitem.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LauncherController"}, {"name": "QML.Singleton", "value": "true"}], "className": "LauncherController", "lineNumber": 14, "methods": [{"access": "public", "index": 5, "name": "hideWithTimer", "returnType": "void"}, {"access": "public", "arguments": [{"name": "avoidHide", "type": "bool"}], "index": 6, "name": "setAvoidHide", "returnType": "void"}, {"access": "public", "index": 7, "name": "cancelHide", "returnType": "void"}, {"access": "public", "arguments": [{"name": "f", "type": "QFont"}, {"name": "weight", "type": "QFont::Weight"}], "index": 8, "name": "adjustFontWeight", "returnType": "QFont"}, {"access": "public", "index": 9, "name": "closeAllPopups", "returnType": "void"}, {"access": "public", "index": 10, "name": "showHelp", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "visible", "notify": "visibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "currentFrame", "notify": "currentFrameChanged", "read": "currentFrame", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCurrentFrame"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "Visible", "notify": "VisibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "LauncherController", "signals": [{"access": "public", "index": 0, "name": "currentFrameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 1, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "Closed", "returnType": "void"}, {"access": "public", "index": 3, "name": "Shown", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 4, "name": "VisibleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "launchercontroller.h", "outputRevision": 68}]