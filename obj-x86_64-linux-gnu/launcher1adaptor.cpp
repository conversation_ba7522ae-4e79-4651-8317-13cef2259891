/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Source file was org.deepin.dde.Launcher1.xml
 *
 * qdbusxml2cpp is Copyright (C) The Qt Company Ltd. and other contributors.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "launcher1adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class Launcher1Adaptor
 */

Launcher1Adaptor::Launcher1Adaptor(LauncherController *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

Launcher1Adaptor::~Launcher1Adaptor()
{
    // destructor
}

bool Launcher1Adaptor::visible() const
{
    // get the value of property Visible
    return qvariant_cast< bool >(parent()->property("Visible"));
}

void Launcher1Adaptor::Exit()
{
    // handle method call org.deepin.dde.Launcher1.Exit
    parent()->Exit();
}

void Launcher1Adaptor::Hide()
{
    // handle method call org.deepin.dde.Launcher1.Hide
    parent()->Hide();
}

void Launcher1Adaptor::Show()
{
    // handle method call org.deepin.dde.Launcher1.Show
    parent()->Show();
}

void Launcher1Adaptor::ShowByMode(qlonglong in0)
{
    // handle method call org.deepin.dde.Launcher1.ShowByMode
    parent()->ShowByMode(in0);
}

void Launcher1Adaptor::Toggle()
{
    // handle method call org.deepin.dde.Launcher1.Toggle
    parent()->Toggle();
}


#include "moc_launcher1adaptor.cpp"
