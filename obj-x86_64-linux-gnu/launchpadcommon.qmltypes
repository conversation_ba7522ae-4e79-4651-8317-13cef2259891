import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "debughelper.h"
        name: "DebugHelper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.launchpad/DebugHelper 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Property {
            name: "qtDebugEnabled"
            type: "bool"
            read: "qtDebugEnabled"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property { name: "useRegularWindow"; type: "bool"; notify: "onUseRegularWindowChanged"; index: 1 }
        Property { name: "avoidLaunchApp"; type: "bool"; notify: "onAvoidLaunchAppChanged"; index: 2 }
        Property { name: "avoidHideWindow"; type: "bool"; notify: "onAvoidHideWindowChanged"; index: 3 }
        Property {
            name: "itemBoundingEnabled"
            type: "bool"
            notify: "onItemBoundingEnabledChanged"
            index: 4
        }
        Signal {
            name: "onUseRegularWindowChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "onAvoidLaunchAppChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "onAvoidHideWindowChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "onItemBoundingEnabledChanged"
            Parameter { type: "bool" }
        }
    }
    Component {
        file: "debughelper.h"
        name: "DebugQuickItem"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.launchpad/DebugItem 1.0"]
        exportMetaObjectRevisions: [256]
        attachedType: "DebugQuickItem"
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "colorChanged" }
    }
    Component {
        file: "desktopintegration.h"
        name: "DesktopIntegration"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.launchpad/DesktopIntegration 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Property {
            name: "dockPosition"
            type: "Qt::ArrowType"
            read: "dockPosition"
            notify: "dockPositionChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "dockGeometry"
            type: "QRect"
            read: "dockGeometry"
            notify: "dockGeometryChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "dockSpacing"
            type: "uint"
            read: "dockSpacing"
            notify: "dockSpacingChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "backgroundUrl"
            type: "QString"
            read: "backgroundUrl"
            notify: "backgroundUrlChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            notify: "opacityChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "scaleFactor"
            type: "double"
            read: "scaleFactor"
            notify: "scaleFactorChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal { name: "dockPositionChanged" }
        Signal { name: "dockGeometryChanged" }
        Signal { name: "dockSpacingChanged" }
        Signal { name: "backgroundUrlChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "scaleFactorChanged" }
        Method { name: "currentDE"; type: "QString" }
        Method { name: "isTreeLand"; type: "bool" }
        Method { name: "openSystemSettings" }
        Method {
            name: "launchByDesktopId"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "environmentVariable"
            type: "QString"
            Parameter { name: "env"; type: "QString" }
        }
        Method {
            name: "disableScale"
            type: "double"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "setDisableScale"
            Parameter { name: "desktopId"; type: "QString" }
            Parameter { name: "disableScale"; type: "double" }
        }
        Method {
            name: "showFolder"
            Parameter { name: "location"; type: "QStandardPaths::StandardLocation" }
        }
        Method {
            name: "showUrl"
            Parameter { name: "url"; type: "QString" }
        }
        Method {
            name: "appIsCompulsoryForDesktop"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "appIsDummyPackage"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "isDockedApp"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "sendToDock"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "removeFromDock"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "isOnDesktop"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "sendToDesktop"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "removeFromDesktop"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "isAutoStart"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "setAutoStart"
            Parameter { name: "desktopId"; type: "QString" }
            Parameter { name: "on"; type: "bool" }
        }
        Method {
            name: "setAutoStart"
            isCloned: true
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "shouldSkipConfirmUninstallDialog"
            type: "bool"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method {
            name: "uninstallApp"
            Parameter { name: "desktopId"; type: "QString" }
        }
        Method { name: "scaleFactor"; type: "double" }
    }
    Component {
        file: "inputeventitem.h"
        name: "InputEventItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: ["org.deepin.launchpad/InputEventItem 1.0"]
        exportMetaObjectRevisions: [256]
        Signal {
            name: "inputReceived"
            Parameter { name: "input"; type: "QString" }
        }
    }
    Component {
        file: "launchercontroller.h"
        name: "LauncherController"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.launchpad/LauncherController 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Property {
            name: "visible"
            type: "bool"
            read: "visible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
        }
        Property {
            name: "currentFrame"
            type: "QString"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "currentFrameChanged"
            index: 1
        }
        Property {
            name: "Visible"
            type: "bool"
            read: "visible"
            notify: "VisibleChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "currentFrameChanged" }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal { name: "Closed" }
        Signal { name: "Shown" }
        Signal {
            name: "VisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Method { name: "hideWithTimer" }
        Method {
            name: "setAvoidHide"
            Parameter { name: "avoidHide"; type: "bool" }
        }
        Method { name: "cancelHide" }
        Method {
            name: "adjustFontWeight"
            type: "QFont"
            Parameter { name: "f"; type: "QFont" }
            Parameter { name: "weight"; type: "QFont::Weight" }
        }
        Method { name: "closeAllPopups" }
        Method { name: "showHelp" }
    }
}
