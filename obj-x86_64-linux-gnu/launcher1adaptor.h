/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Source file was org.deepin.dde.Launcher1.xml
 *
 * qdbusxml2cpp is Copyright (C) The Qt Company Ltd. and other contributors.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef LAUNCHER1ADAPTOR_H
#define LAUNCHER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "launchercontroller.h"
#include <QtCore/qcontainerfwd.h>

/*
 * Adaptor class for interface org.deepin.dde.Launcher1
 */
class Launcher1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.deepin.dde.Launcher1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.deepin.dde.Launcher1\">\n"
"    <property access=\"read\" type=\"b\" name=\"Visible\"/>\n"
"    <method name=\"Exit\"/>\n"
"    <method name=\"Show\"/>\n"
"    <method name=\"Hide\"/>\n"
"    <method name=\"Toggle\"/>\n"
"    <method name=\"ShowByMode\">\n"
"      <arg direction=\"in\" type=\"x\"/>\n"
"    </method>\n"
"    <signal name=\"Closed\"/>\n"
"    <signal name=\"Shown\"/>\n"
"    <signal name=\"VisibleChanged\">\n"
"      <arg direction=\"out\" type=\"b\" name=\"visible\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    Launcher1Adaptor(LauncherController *parent);
    ~Launcher1Adaptor() override;

    inline LauncherController *parent() const
    { return static_cast<LauncherController *>(QObject::parent()); }

public: // PROPERTIES
    Q_PROPERTY(bool Visible READ visible)
    bool visible() const;

public Q_SLOTS: // METHODS
    void Exit();
    void Hide();
    void Show();
    void ShowByMode(qlonglong in0);
    void Toggle();
Q_SIGNALS: // SIGNALS
    void Closed();
    void Shown();
    void VisibleChanged(bool visible);
};

#endif
